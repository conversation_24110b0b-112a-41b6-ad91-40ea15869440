// auth.js
import NextAuth from "next-auth";
import { MongoDBAdapter } from "@auth/mongodb-adapter";
import Google from "next-auth/providers/google";
import Facebook from "next-auth/providers/facebook";
import Credentials from "next-auth/providers/credentials";
import Nodemailer from "next-auth/providers/nodemailer";
import { createTransport } from "nodemailer";
import clientPromise from "./libs/mongoDb/mongodb-client-promise"; // Import MongoDB client
import { comparePassword, hashPassword, generateToken } from "./libs/utils";

// Create a resilient adapter that handles connection failures
let mongoAdapter;
try {
  mongoAdapter = MongoDBAdapter(clientPromise, {
    databaseName: "luyari" // Explicitly specify database name
  });
} catch (error) {
  console.warn('MongoDB adapter initialization failed, falling back to JWT sessions:', error.message);
  mongoAdapter = null;
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  // Use MongoDB adapter if available, otherwise fall back to JWT
  ...(mongoAdapter && { adapter: mongoAdapter }),

  // Use database strategy if adapter is available, otherwise JWT
  session: {
    strategy: mongoAdapter ? "database" : "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },

  // Allow linking accounts with same email (be careful with this in production)
  allowDangerousEmailAccountLinking: true,
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
    }),
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const email = credentials.email;
        const password = credentials.password;

        console.log("🔐 Credentials authorize called with email:", email);

        try {
          // Get MongoDB client and database
          const client = await clientPromise;
          const db = client.db();

          // Find user by email in MongoDB
          const user = await db.collection("users").findOne({ email });
          console.log("👤 User found in database:", user ? "Yes" : "No");

          if (user && user.password) {
            console.log("🔑 User has password, checking validity...");
            // Compare the provided password with the stored hashed password
            const isPasswordValid = await comparePassword(password, user.password);
            console.log("✅ Password valid:", isPasswordValid);

            if (isPasswordValid) {
              // Return user object without sensitive information
              // NextAuth.js will handle mapping this to the session/JWT
              const userResult = {
                id: user._id.toString(),
                name: user.name,
                email: user.email,
                role: user.role || "user"
              };
              console.log("🎉 Returning user:", userResult);
              return userResult;
            }
          }

          // If login fails, return null
          console.log("❌ Authentication failed - invalid credentials");
          return null; // Return null instead of throwing error for better UX
        } catch (error) {
          console.error("💥 Authorization error:", error);
          return null;
        }
      },
    }),
    Nodemailer({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: parseInt(process.env.EMAIL_SERVER_PORT),
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
      async sendVerificationRequest({ identifier: email, url, provider }) {
        const { host } = new URL(url);
        const transport = createTransport(provider.server);

        try {
          await transport.sendMail({
            to: email,
            from: provider.from,
            subject: `Sign in to ${host}`,
            html: `
              <div style="font-family: sans-serif; text-align: center; padding: 20px;">
                <h1 style="color: #333;">Sign in to ${host}</h1>
                <p>Click the link below to sign in:</p>
                <a href="${url}" style="display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px;">
                  Sign in
                </a>
                <p>If you did not request this, you can safely ignore this email.</p>
              </div>
            `,
          });
          console.log(`Magic link sent to ${email}`);
        } catch (error) {
          console.error(`Failed to send magic link email to ${email}:`, error);
          throw new Error("Failed to send magic link email.");
        }
      },
    }),
  ],
  callbacks: {
    // Enhanced signIn callback for database adapter with manual account linking
    async signIn({ user, account, profile }) {
      console.log("🔐 signIn callback - user:", user?.email, "provider:", account?.provider);

      // For credentials provider, just return true (user already validated)
      if (account?.provider === "credentials") {
        console.log("✅ Credentials sign-in approved");
        return true;
      }

      // For OAuth providers with database adapter, handle account linking manually
      if ((account?.provider === "google" || account?.provider === "facebook") && mongoAdapter) {
        try {
          const client = await clientPromise;
          const db = client.db();

          // Check if user exists with this email
          const existingUser = await db.collection("users").findOne({ email: user.email });

          if (existingUser) {
            // Check if this OAuth account is already linked
            const existingAccount = await db.collection("accounts").findOne({
              userId: existingUser._id.toString(),
              provider: account.provider,
              providerAccountId: account.providerAccountId
            });

            if (!existingAccount) {
              // Manually link the OAuth account to existing user
              console.log("🔗 Manually linking OAuth account to existing user:", user.email);

              const accountData = {
                userId: existingUser._id.toString(),
                type: account.type,
                provider: account.provider,
                providerAccountId: account.providerAccountId,
                access_token: account.access_token,
                expires_at: account.expires_at,
                id_token: account.id_token,
                scope: account.scope,
                token_type: account.token_type,
                createdAt: new Date(),
                updatedAt: new Date()
              };

              await db.collection("accounts").insertOne(accountData);

              // Update user with OAuth info
              await db.collection("users").updateOne(
                { _id: existingUser._id },
                {
                  $set: {
                    name: user.name || existingUser.name,
                    image: user.image || existingUser.image,
                    emailVerified: new Date(),
                    lastLogin: new Date(),
                    updatedAt: new Date()
                  }
                }
              );

              console.log("✅ OAuth account successfully linked to existing user");
              return true;
            } else {
              console.log("✅ OAuth account already linked, allowing sign-in");
              return true;
            }
          } else {
            // No existing user, allow creation
            console.log("✅ New OAuth user, allowing creation");
            return true;
          }
        } catch (error) {
          console.error("💥 Error in signIn callback (falling back to JWT):", error);
          // Fall back to allowing sign-in with JWT strategy
          console.log("🔄 Falling back to JWT strategy for OAuth sign-in");
          return true;
        }
      }

      return true;
    },
    // JWT callback - handles both database and JWT strategies
    async jwt({ token, user, account, profile }) {
      console.log('🎫 JWT callback - strategy:', mongoAdapter ? 'database' : 'jwt', 'user:', user?.email);

      if (user) {
        // Add custom properties to token
        token.id = user.id || user._id?.toString();
        token.role = user.role || "user";
        token.name = user.name;
        token.email = user.email;
        token.picture = user.image || profile?.picture;
      }

      // Store access token if available
      if (account) {
        token.accessToken = account.access_token;

        // For JWT strategy, store provider info
        if (!mongoAdapter) {
          token.provider = account.provider;
        }
      }

      return token;
    },
    // Session callback - handles both database and JWT strategies
    async session({ session, user, token }) {
      // console.log('📋 Session callback - strategy:', mongoAdapter ? 'database' : 'jwt');

      // With database adapter, user object is available directly
      if (user && mongoAdapter) {
        session.user.id = user.id;
        session.user.role = user.role || "user";
        session.user.name = user.name;
        session.user.email = user.email;
        session.user.image = user.image;
      } else if (token) {
        // JWT strategy or fallback to token
        session.user.id = token.id || token.sub;
        session.user.role = token.role || "user";
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.image = token.picture || token.image;
      }

      return session;
    },
    // Optionally, you can add a redirect callback if you need custom redirect logic
    // async redirect({ url, baseUrl }) {
    //   // Allows relative callback URLs
    //   if (url.startsWith("/")) return `${baseUrl}${url}`
    //   // Allows absolute callback URLs for same origin
    //   else if (new URL(url).origin === baseUrl) return url
    //   return baseUrl
    // }
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/signin?error=", // Redirect to login page on error, pass error message as query param
    verifyRequest: "/auth/signin?verifyRequest=true", // Redirect to login page after magic link email sent
  },
  events: {
    async signIn(message) {
      console.log("✅ User signed in:", message.user.email);

      // Update last login time for existing users (only if MongoDB adapter is available)
      if (mongoAdapter) {
        try {
          const client = await clientPromise;
          const db = client.db();

          await db.collection("users").updateOne(
            { email: message.user.email },
            { $set: { lastLogin: new Date() } }
          );
        } catch (error) {
          console.error("Error updating last login:", error);
        }
      }
    },
    async signOut(message) {
      console.log("👋 User signed out:", message.session?.user?.email || message.token?.email);
    },
    async createUser(message) {
      console.log("🆕 User created:", message.user.email);

      // Set default role for new users (only if MongoDB adapter is available)
      if (mongoAdapter) {
        try {
          const client = await clientPromise;
          const db = client.db();

          await db.collection("users").updateOne(
            { email: message.user.email },
            { $set: { role: "user", createdAt: new Date(), updatedAt: new Date() } }
          );
        } catch (error) {
          console.error("Error setting default role:", error);
        }
      }
    },
    async linkAccount(message) {
      console.log("🔗 Account linked:", message.user.email, message.account.provider);

      // Mark email as verified when linking OAuth account (only if MongoDB adapter is available)
      if (mongoAdapter) {
        try {
          const client = await clientPromise;
          const db = client.db();

          await db.collection("users").updateOne(
            { email: message.user.email },
            { $set: { emailVerified: new Date(), updatedAt: new Date() } }
          );
        } catch (error) {
          console.error("Error updating email verification:", error);
        }
      }
    },
  },
  debug: process.env.NODE_ENV === "development", // Enable debug logs in development
});

// --- Custom Password Reset & Registration Logic (Interacting with Mongoose) ---

/**
 * Initiates a password reset by sending a magic link email.
 * @param {string} email - The email of the user requesting a reset.
 * @returns {Promise<boolean>} True if email sent successfully, false otherwise.
 */
export async function sendPasswordResetEmail(email) {
  try {
    // Get MongoDB client and database
    const client = await clientPromise;
    const db = client.db();

    const user = await db.collection("users").findOne({ email });

    if (!user) {
      console.log(`Password reset requested for non-existent email: ${email}`);
      return true; // Still return true to avoid leaking user existence
    }

    const token = generateToken();
    const expires = new Date(Date.now() + 3600 * 1000); // Token valid for 1 hour

    // Invalidate any existing reset tokens for this user
    await db.collection("verification_tokens").deleteMany({
      identifier: email,
      type: 'password_reset'
    });

    // Create a new password reset token in the verification_tokens collection
    await db.collection("verification_tokens").insertOne({
      identifier: email,
      token: token,
      expires: expires,
      type: 'password_reset', // Custom type to distinguish from magic links
    });

    const resetUrl = `${process.env.AUTH_URL}/auth/reset-password/${token}`;
    const transport = createTransport({
      host: process.env.EMAIL_SERVER_HOST,
      port: parseInt(process.env.EMAIL_SERVER_PORT),
      auth: {
        user: process.env.EMAIL_SERVER_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD,
      },
    });

    await transport.sendMail({
      to: email,
      from: process.env.EMAIL_FROM,
      subject: `Password Reset for your account`,
      html: `
        <div style="font-family: sans-serif; text-align: center; padding: 20px;">
          <h1 style="color: #333;">Password Reset Request</h1>
          <p>You requested a password reset for your account.</p>
          <p>Click the link below to reset your password:</p>
          <a href="${resetUrl}" style="display: inline-block; padding: 10px 20px; background-color: #ef4444; color: white; text-decoration: none; border-radius: 5px;">
            Reset Password
          </a>
          <p>This link is valid for 1 hour.</p>
          <p>If you did not request this, you can safely ignore this email.</p>
        </div>
      `,
    });
    console.log(`Password reset email sent to ${email}`);
    return true;
  } catch (error) {
    console.error(`Failed to send password reset email to ${email}:`, error);
    return false;
  }
}

/**
 * Resets a user's password using a valid token.
 * @param {string} token - The password reset token.
 * @param {string} newPassword - The new plain text password.
 * @returns {Promise<boolean>} True if password was reset successfully, false otherwise.
 */
export async function resetUserPassword(token, newPassword) {
  try {
    // Get MongoDB client and database
    const client = await clientPromise;
    const db = client.db();

    // Find the password reset token
    const resetToken = await db.collection("verification_tokens").findOne({
      token,
      type: 'password_reset'
    });

    if (!resetToken || resetToken.expires < new Date()) {
      console.warn("Invalid or expired password reset token.");
      return false;
    }

    const user = await db.collection("users").findOne({ email: resetToken.identifier });
    if (!user) {
      console.warn("User not found for password reset token.");
      return false;
    }

    const hashedPassword = await hashPassword(newPassword);

    // Update user password and mark email as verified
    await db.collection("users").updateOne(
      { _id: user._id },
      {
        $set: {
          password: hashedPassword,
          emailVerified: new Date()
        }
      }
    );

    // Invalidate token after use
    await db.collection("verification_tokens").deleteOne({ _id: resetToken._id });

    console.log(`Password for user ${user.email} reset successfully.`);
    return true;
  } catch (error) {
    console.error("Error resetting password:", error);
    return false;
  }
}

/**
 * Registers a new user with email and password.
 * This is a custom function, not part of NextAuth.js core providers.
 * @param {string} name
 * @param {string} email
 * @param {string} password
 * @returns {Promise<object|null>} The new user object if successful, null otherwise.
 */
export async function registerUser(name, email, password) {
  try {
    // Get MongoDB client and database
    const client = await clientPromise;
    const db = client.db();

    const existingUser = await db.collection("users").findOne({ email });

    if (existingUser) {
      return null; // User already exists
    }

    const hashedPassword = await hashPassword(password);
    const newUser = {
      name,
      email,
      password: hashedPassword,
      emailVerified: null,
      image: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection("users").insertOne(newUser);
    return { ...newUser, id: result.insertedId.toString() };
  } catch (error) {
    console.error("Error registering user:", error);
    return null;
  }
}
