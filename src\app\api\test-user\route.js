// API route to create a test user for debugging authentication
import { NextResponse } from "next/server";
import clientPromise from "../../../libs/mongoDb/mongodb-client-promise";
import { hashPassword } from "../../../libs/utils";

export async function GET() {
  try {
    const client = await clientPromise;
    const db = client.db();
    
    // Check if test user exists
    const testUser = await db.collection("users").findOne({ email: "<EMAIL>" });
    
    if (testUser) {
      return NextResponse.json({ 
        message: "Test user already exists",
        user: {
          email: testUser.email,
          name: testUser.name,
          role: testUser.role,
          hasPassword: !!testUser.password
        }
      });
    } else {
      return NextResponse.json({ 
        message: "Test user does not exist",
        suggestion: "Use POST to create test user"
      });
    }
  } catch (error) {
    console.error("Error checking test user:", error);
    return NextResponse.json({ error: "Database error" }, { status: 500 });
  }
}

export async function POST() {
  try {
    const client = await clientPromise;
    const db = client.db();
    
    // Check if test user already exists
    const existingUser = await db.collection("users").findOne({ email: "<EMAIL>" });
    
    if (existingUser) {
      return NextResponse.json({ 
        message: "Test user already exists",
        user: {
          email: existingUser.email,
          name: existingUser.name,
          role: existingUser.role
        }
      });
    }
    
    // Create test user
    const hashedPassword = await hashPassword("password123");
    const testUser = {
      name: "Test Admin",
      email: "<EMAIL>",
      password: hashedPassword,
      role: "admin",
      emailVerified: new Date(),
      image: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await db.collection("users").insertOne(testUser);
    
    return NextResponse.json({ 
      message: "Test user created successfully",
      user: {
        id: result.insertedId.toString(),
        email: testUser.email,
        name: testUser.name,
        role: testUser.role
      },
      credentials: {
        email: "<EMAIL>",
        password: "password123"
      }
    });
  } catch (error) {
    console.error("Error creating test user:", error);
    return NextResponse.json({ error: "Database error" }, { status: 500 });
  }
}
