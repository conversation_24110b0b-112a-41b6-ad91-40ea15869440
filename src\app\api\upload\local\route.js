// src/app/api/upload/local/route.js
// Local file upload API endpoint (offline fallback)

import { NextResponse } from "next/server";
import { auth } from "../../../../auth";
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * POST /api/upload/local - Upload file to local uploads folder
 */
export async function POST(request) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const projectTitle = formData.get('projectTitle');
    const category = formData.get('category');
    const fileId = formData.get('fileId');

    if (!file || !projectTitle || !category || !fileId) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get file extension
    const fileExtension = file.name.split('.').pop();
    const fileName = `${fileId}.${fileExtension}`;

    // Create directory structure
    const uploadsDir = join(process.cwd(), 'uploads');
    const projectDir = join(uploadsDir, projectTitle);
    const categoryDir = join(projectDir, category);

    // Ensure directories exist
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }
    if (!existsSync(projectDir)) {
      await mkdir(projectDir, { recursive: true });
    }
    if (!existsSync(categoryDir)) {
      await mkdir(categoryDir, { recursive: true });
    }

    // Write file
    const filePath = join(categoryDir, fileName);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    await writeFile(filePath, buffer);

    // Return local URL
    const localUrl = `/uploads/${projectTitle}/${category}/${fileName}`;

    return NextResponse.json({
      success: true,
      url: localUrl,
      message: "File uploaded to local storage"
    });

  } catch (error) {
    console.error("Local upload error:", error);
    return NextResponse.json(
      { message: "Upload failed", error: error.message },
      { status: 500 }
    );
  }
}
