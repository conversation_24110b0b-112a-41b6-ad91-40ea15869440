// src/components/UserActivity/UserActivity.jsx
import React from 'react';
import ProfileSection from './ProfileSection';

const UserActivity = ({ activities }) => {
  if (!activities || activities.length === 0) {
    return (
      <ProfileSection title="Recent Activity">
        <p className="text-gray-600">No recent activity to display.</p>
      </ProfileSection>
    );
  }

  return (
    <ProfileSection title="Recent Activity">
      <ul className="list-none p-0 space-y-3">
        {activities.map(activity => (
          <li key={activity.id} className="mb-3 p-3 border-l-4 border-blue-500 bg-gray-50 rounded-md">
            <span className="font-bold text-gray-700 mr-2">
              {new Date(activity.date).toLocaleDateString()} -
            </span>
            <span className="font-bold text-blue-600 mr-2">
              {activity.type.charAt(0).toUpperCase() + activity.type.slice(1)}:
            </span>
            <span className="text-gray-800">
              {activity.description}
            </span>
          </li>
        ))}
      </ul>
    </ProfileSection>
  );
};

export default UserActivity;