'use client'
import { useRef, useEffect, useState } from 'react'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'

export default function ARController({ 
  onSelect, 
  onSelectStart, 
  onSelectEnd,
  onSqueeze,
  enabled = true 
}) {
  const { gl } = useThree()
  const controllerRef = useRef()
  const controller1Ref = useRef()
  const [isSelecting, setIsSelecting] = useState(false)

  useEffect(() => {
    if (!gl.xr || !enabled) return

    // Get XR controllers
    const controller0 = gl.xr.getController(0)
    const controller1 = gl.xr.getController(1)
    
    controllerRef.current = controller0
    controller1Ref.current = controller1

    // Select event handlers
    const handleSelect = (event) => {
      console.log('🎮 Controller select triggered')
      setIsSelecting(false)
      onSelect?.(event, controller0)
    }

    const handleSelectStart = (event) => {
      console.log('🎮 Controller select start')
      setIsSelecting(true)
      onSelectStart?.(event, controller0)
    }

    const handleSelectEnd = (event) => {
      console.log('🎮 Controller select end')
      setIsSelecting(false)
      onSelectEnd?.(event, controller0)
    }

    const handleSqueeze = (event) => {
      console.log('🎮 Controller squeeze')
      onSqueeze?.(event, controller0)
    }

    // Add event listeners
    controller0.addEventListener('select', handleSelect)
    controller0.addEventListener('selectstart', handleSelectStart)
    controller0.addEventListener('selectend', handleSelectEnd)
    controller0.addEventListener('squeeze', handleSqueeze)

    // Also handle controller 1 if available
    if (controller1) {
      controller1.addEventListener('select', handleSelect)
      controller1.addEventListener('selectstart', handleSelectStart)
      controller1.addEventListener('selectend', handleSelectEnd)
      controller1.addEventListener('squeeze', handleSqueeze)
    }

    return () => {
      // Cleanup event listeners
      controller0.removeEventListener('select', handleSelect)
      controller0.removeEventListener('selectstart', handleSelectStart)
      controller0.removeEventListener('selectend', handleSelectEnd)
      controller0.removeEventListener('squeeze', handleSqueeze)

      if (controller1) {
        controller1.removeEventListener('select', handleSelect)
        controller1.removeEventListener('selectstart', handleSelectStart)
        controller1.removeEventListener('selectend', handleSelectEnd)
        controller1.removeEventListener('squeeze', handleSqueeze)
      }
    }
  }, [gl.xr, enabled, onSelect, onSelectStart, onSelectEnd, onSqueeze])

  return null // This component doesn't render anything visible
}

// Hook for accessing controller state
export function useARController() {
  const { gl } = useThree()
  const [controllers, setControllers] = useState([])
  const [isSelecting, setIsSelecting] = useState(false)

  useEffect(() => {
    if (!gl.xr) return

    const controller0 = gl.xr.getController(0)
    const controller1 = gl.xr.getController(1)
    
    setControllers([controller0, controller1].filter(Boolean))

    const handleSelectStart = () => setIsSelecting(true)
    const handleSelectEnd = () => setIsSelecting(false)

    controller0.addEventListener('selectstart', handleSelectStart)
    controller0.addEventListener('selectend', handleSelectEnd)
    
    if (controller1) {
      controller1.addEventListener('selectstart', handleSelectStart)
      controller1.addEventListener('selectend', handleSelectEnd)
    }

    return () => {
      controller0.removeEventListener('selectstart', handleSelectStart)
      controller0.removeEventListener('selectend', handleSelectEnd)
      
      if (controller1) {
        controller1.removeEventListener('selectstart', handleSelectStart)
        controller1.removeEventListener('selectend', handleSelectEnd)
      }
    }
  }, [gl.xr])

  return {
    controllers,
    isSelecting,
    getController: (index = 0) => controllers[index],
    hasControllers: controllers.length > 0
  }
}

// Visual controller representation
export function ARControllerVisual({ 
  controllerIndex = 0, 
  showRay = true, 
  rayColor = "#ffffff",
  rayLength = 10 
}) {
  const { gl } = useThree()
  const controllerRef = useRef()
  const rayRef = useRef()

  useEffect(() => {
    if (!gl.xr) return

    const controller = gl.xr.getController(controllerIndex)
    controllerRef.current = controller

    // Add visual ray
    if (showRay && rayRef.current) {
      const geometry = new THREE.BufferGeometry()
      const positions = new Float32Array([0, 0, 0, 0, 0, -rayLength])
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
      
      const material = new THREE.LineBasicMaterial({ 
        color: rayColor,
        transparent: true,
        opacity: 0.7
      })
      
      const line = new THREE.Line(geometry, material)
      controller.add(line)
      
      return () => {
        controller.remove(line)
        geometry.dispose()
        material.dispose()
      }
    }
  }, [gl.xr, controllerIndex, showRay, rayColor, rayLength])

  return (
    <group ref={controllerRef}>
      {/* Controller visual representation */}
      <mesh position={[0, 0, -0.05]}>
        <boxGeometry args={[0.02, 0.02, 0.1]} />
        <meshBasicMaterial color="#333333" />
      </mesh>
      
      {/* Controller tip indicator */}
      <mesh position={[0, 0, -0.1]}>
        <sphereGeometry args={[0.005, 8, 8]} />
        <meshBasicMaterial color={rayColor} />
      </mesh>
    </group>
  )
}

// Touch/tap handler for mobile AR
export function ARTouchHandler({ onTap, enabled = true }) {
  useEffect(() => {
    if (!enabled) return

    const handleTouchEnd = (event) => {
      // Prevent default touch behavior
      event.preventDefault()
      
      // Get touch position
      const touch = event.changedTouches[0]
      const touchPosition = {
        x: (touch.clientX / window.innerWidth) * 2 - 1,
        y: -(touch.clientY / window.innerHeight) * 2 + 1
      }
      
      console.log('📱 Touch tap at:', touchPosition)
      onTap?.(touchPosition, event)
    }

    // Add touch event listener
    document.addEventListener('touchend', handleTouchEnd, { passive: false })

    return () => {
      document.removeEventListener('touchend', handleTouchEnd)
    }
  }, [enabled, onTap])

  return null
}
