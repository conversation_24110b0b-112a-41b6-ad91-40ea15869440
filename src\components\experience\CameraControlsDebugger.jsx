'use client';
import React, { useEffect, useState } from 'react';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import { Html } from '@react-three/drei';

/**
 * Debug component to monitor camera controls state and help identify issues
 * This component renders HTML inside the Three.js canvas using Html from drei
 */
export default function CameraControlsDebugger({ camera, scene }) {
  const { experienceState } = useExperienceContext();
  const [debugInfo, setDebugInfo] = useState({});

  useEffect(() => {
    const updateDebugInfo = () => {
      const info = {
        timestamp: new Date().toLocaleTimeString(),
        cameraPosition: camera ? camera.position.toArray().map(n => n.toFixed(2)) : 'No camera',
        sceneChildren: scene ? scene.children.length : 'No scene',
        activeRoomSnap: experienceState?.activeRoomSnap || 'None',
        firstPersonView: experienceState?.firstPersonView || false,
        mode360: experienceState?.mode360 || false,
        modeModel: experienceState?.modeModel || false,
        foundSnapObject: null
      };

      // Check if the active snap object exists in the scene
      if (experienceState?.activeRoomSnap && scene) {
        const foundObject = scene.getObjectByName(experienceState.activeRoomSnap);
        info.foundSnapObject = foundObject ? 'Found' : 'Not found';
        if (foundObject) {
          info.objectPosition = foundObject.position.toArray().map(n => n.toFixed(2));
        }
      }

      setDebugInfo(info);
    };

    // Update debug info every second
    const interval = setInterval(updateDebugInfo, 1000);
    updateDebugInfo(); // Initial update

    return () => clearInterval(interval);
  }, [camera, scene, experienceState]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Html
      position={[-8, 4, 0]}
      transform={false}
      occlude={false}
      style={{
        pointerEvents: 'none',
        userSelect: 'none'
      }}
    >
      <div className="bg-black/80 text-white p-3 rounded-lg text-xs font-mono max-w-sm">
        <div className="text-yellow-400 font-bold mb-2">Camera Debug Info</div>
        <div className="space-y-1">
          <div>Time: {debugInfo.timestamp}</div>
          <div>Camera: [{debugInfo.cameraPosition?.join(', ') || 'N/A'}]</div>
          <div>Scene Children: {debugInfo.sceneChildren}</div>
          <div>Active Snap: {debugInfo.activeRoomSnap}</div>
          <div>Snap Object: {debugInfo.foundSnapObject || 'N/A'}</div>
          {debugInfo.objectPosition && (
            <div>Object Pos: [{debugInfo.objectPosition.join(', ')}]</div>
          )}
          <div>First Person: {debugInfo.firstPersonView ? 'Yes' : 'No'}</div>
          <div>Mode 360: {debugInfo.mode360 ? 'Yes' : 'No'}</div>
          <div>Mode Model: {debugInfo.modeModel ? 'Yes' : 'No'}</div>
        </div>
      </div>
    </Html>
  );
}
