// src/components/forms/BuildingForm.jsx
// Main building form component that orchestrates all form inputs

"use client";

import { useState, useEffect } from 'react';
import TextInput from './TextInput';
import SelectInput from './SelectInput';
import ArrayInput from './ArrayInput';
import ObjectInput from './ObjectInput';
import HighlightsInput from './HighlightsInput';
import FileUpload from './FileUpload';

/**
 * BuildingForm Component
 * @param {Object} props
 * @param {Object} props.initialData - Initial form data for editing
 * @param {Function} props.onSubmit - Form submission handler
 * @param {Function} props.onCancel - Form cancellation handler
 * @param {boolean} props.isLoading - Loading state
 * @param {string} props.mode - 'create' or 'edit'
 */
export default function BuildingForm({
  initialData = null,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create'
}) {
  const [formData, setFormData] = useState({
    // Text fields
    projectTitle: '',
    price: '',
    buildingTitle: '',
    buildingType: '',
    desc: '',
    position: '',
    arPosition: '',
    minDistance: '',
    maxDistance: '',
    buildingRole: 'exhibit',
    features: '',
    outroSection: '',
    warrantyInformation: '',
    shippingInformation: '',
    returnPolicy: '',

    // Object field
    buildingSummary: {
      length: '',
      width: '',
      baths: '',
      levels: '',
      cars: '',
      beds: ''
    },

    // Array fields
    buildingHighlights: [],
    tags: [],
    color: [],
    collections: [],

    // File upload fields
    renders: [],
    drawings: [],
    _360sImages: [],
    modelsFiles: [],
    hideLevel: [],
    supportFiles: [],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: []
  });

  const [errors, setErrors] = useState({});
  const [isDirty, setIsDirty] = useState(false);

  // Initialize form with existing data for editing
  useEffect(() => {
    if (initialData && mode === 'edit') {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [initialData, mode]);

  const handleInputChange = (value, name) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setIsDirty(true);

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required text fields
    const requiredFields = [
      'projectTitle', 'price', 'buildingTitle', 'buildingType', 
      'desc', 'position', 'arPosition', 'minDistance', 'maxDistance', 
      'features', 'outroSection'
    ];

    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].trim() === '') {
        newErrors[field] = `${field.replace(/([A-Z])/g, ' $1').toLowerCase()} is required`;
      }
    });

    // Validate buildingType
    const validBuildingTypes = ['multi-storey', 'single-storey', 'multi-residence'];
    if (formData.buildingType && !validBuildingTypes.includes(formData.buildingType)) {
      newErrors.buildingType = 'Invalid building type';
    }

    // Validate buildingSummary
    const summaryErrors = {};
    const requiredSummaryFields = ['length', 'width', 'baths', 'levels', 'cars', 'beds'];
    
    requiredSummaryFields.forEach(field => {
      const value = formData.buildingSummary[field];
      if (!value || value === '' || isNaN(value) || Number(value) < 0) {
        summaryErrors[field] = `${field} must be a valid positive number`;
      }
    });

    if (Object.keys(summaryErrors).length > 0) {
      newErrors.buildingSummary = summaryErrors;
    }

    // Validate buildingHighlights
    if (!formData.buildingHighlights || formData.buildingHighlights.length === 0) {
      newErrors.buildingHighlights = 'At least one building highlight is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Convert buildingSummary values to numbers
    const processedData = {
      ...formData,
      buildingSummary: {
        length: Number(formData.buildingSummary.length),
        width: Number(formData.buildingSummary.width),
        baths: Number(formData.buildingSummary.baths),
        levels: Number(formData.buildingSummary.levels),
        cars: Number(formData.buildingSummary.cars),
        beds: Number(formData.buildingSummary.beds)
      }
    };

    await onSubmit(processedData);
  };

  const handleReset = () => {
    if (mode === 'create') {
      setFormData({
        projectTitle: '',
        price: '',
        buildingTitle: '',
        buildingType: '',
        desc: '',
        position: '',
        arPosition: '',
        minDistance: '',
        maxDistance: '',
        buildingRole: 'exhibit',
        features: '',
        outroSection: '',
        warrantyInformation: '',
        shippingInformation: '',
        returnPolicy: '',
        buildingSummary: {
          length: '',
          width: '',
          baths: '',
          levels: '',
          cars: '',
          beds: ''
        },
        buildingHighlights: [],
        tags: [],
        color: [],
        collections: [],
        renders: [],
        drawings: [],
        _360sImages: [],
        modelsFiles: [],
        hideLevel: [],
        supportFiles: [],
        roomSnaps: [],
        presentationDrawings: [],
        constructionDrawingsPdf: [],
        constructionDrawingsDwg: []
      });
    } else {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
    setErrors({});
    setIsDirty(false);
  };

  const buildingTypeOptions = [
    { value: 'multi-storey', label: 'Multi-Storey' },
    { value: 'single-storey', label: 'Single-Storey' },
    { value: 'multi-residence', label: 'Multi-Residence' }
  ];

  const buildingSummaryFields = [
    { name: 'length', label: 'Length (m)', type: 'number', required: true, min: 0, step: 0.1 },
    { name: 'width', label: 'Width (m)', type: 'number', required: true, min: 0, step: 0.1 },
    { name: 'baths', label: 'Bathrooms', type: 'number', required: true, min: 0, step: 1 },
    { name: 'levels', label: 'Levels', type: 'number', required: true, min: 1, step: 1 },
    { name: 'cars', label: 'Car Spaces', type: 'number', required: true, min: 0, step: 1 },
    { name: 'beds', label: 'Bedrooms', type: 'number', required: true, min: 0, step: 1 }
  ];

  return (
    <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8 overflow-y-auto overflow-x-hidden h-full">
      {/* Basic Information Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            name="projectTitle"
            label="Project Title"
            value={formData.projectTitle}
            onChange={handleInputChange}
            required
            error={errors.projectTitle}
            helpText="Unique identifier for this building project"
          />
          
          <TextInput
            name="price"
            label="Price"
            value={formData.price}
            onChange={handleInputChange}
            required
            error={errors.price}
            placeholder="e.g., $250,000"
          />
          
          <TextInput
            name="buildingTitle"
            label="Building Title"
            value={formData.buildingTitle}
            onChange={handleInputChange}
            required
            error={errors.buildingTitle}
            placeholder="e.g., Modern Family Home"
          />
          
          <SelectInput
            name="buildingType"
            label="Building Type"
            value={formData.buildingType}
            onChange={handleInputChange}
            options={buildingTypeOptions}
            required
            error={errors.buildingType}
          />
        </div>

        <TextInput
          name="desc"
          label="Description"
          value={formData.desc}
          onChange={handleInputChange}
          required
          multiline
          rows={4}
          error={errors.desc}
          helpText="Detailed description of the building project"
        />
      </div>

      {/* Technical Details Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Technical Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            name="position"
            label="Position"
            value={formData.position}
            onChange={handleInputChange}
            required
            error={errors.position}
          />
          
          <TextInput
            name="arPosition"
            label="AR Position"
            value={formData.arPosition}
            onChange={handleInputChange}
            required
            error={errors.arPosition}
          />
          
          <TextInput
            name="minDistance"
            label="Minimum Distance"
            value={formData.minDistance}
            onChange={handleInputChange}
            required
            error={errors.minDistance}
          />
          
          <TextInput
            name="maxDistance"
            label="Maximum Distance"
            value={formData.maxDistance}
            onChange={handleInputChange}
            required
            error={errors.maxDistance}
          />
          
          <TextInput
            name="buildingRole"
            label="Building Role"
            value={formData.buildingRole}
            onChange={handleInputChange}
            error={errors.buildingRole}
            helpText="Default: exhibit"
          />
        </div>
      </div>

      {/* Building Summary Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Building Summary</h2>
        
        <ObjectInput
          name="buildingSummary"
          label="Building Specifications"
          value={formData.buildingSummary}
          onChange={handleInputChange}
          fields={buildingSummaryFields}
          required
          errors={errors.buildingSummary || {}}
          helpText="Enter the building dimensions and room counts"
        />
      </div>

      {/* Content Sections */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Content & Features</h2>
        
        <div className="space-y-6">
          <TextInput
            name="features"
            label="Features"
            value={formData.features}
            onChange={handleInputChange}
            required
            multiline
            rows={3}
            error={errors.features}
            helpText="List the key features of this building"
          />
          
          <TextInput
            name="outroSection"
            label="Outro Section"
            value={formData.outroSection}
            onChange={handleInputChange}
            required
            multiline
            rows={3}
            error={errors.outroSection}
          />
          
          <HighlightsInput
            name="buildingHighlights"
            label="Building Highlights"
            value={formData.buildingHighlights}
            onChange={handleInputChange}
            required
            error={errors.buildingHighlights}
            helpText="Add key highlights with titles and descriptions"
          />
        </div>
      </div>

      {/* Categories Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Categories & Tags</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <ArrayInput
            name="tags"
            label="Tags"
            value={formData.tags}
            onChange={handleInputChange}
            placeholder="Add tags..."
            helpText="Press Enter or comma to add tags"
          />
          
          <ArrayInput
            name="color"
            label="Colors"
            value={formData.color}
            onChange={handleInputChange}
            placeholder="Add colors..."
            helpText="Building color themes"
          />
          
          <ArrayInput
            name="collections"
            label="Collections"
            value={formData.collections}
            onChange={handleInputChange}
            placeholder="Add collections..."
            helpText="Building collections or series"
          />
        </div>
      </div>

      {/* Additional Information Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Information</h2>
        
        <div className="space-y-6">
          <TextInput
            name="warrantyInformation"
            label="Warranty Information"
            value={formData.warrantyInformation}
            onChange={handleInputChange}
            multiline
            rows={2}
            error={errors.warrantyInformation}
          />
          
          <TextInput
            name="shippingInformation"
            label="Shipping Information"
            value={formData.shippingInformation}
            onChange={handleInputChange}
            multiline
            rows={2}
            error={errors.shippingInformation}
          />
          
          <TextInput
            name="returnPolicy"
            label="Return Policy"
            value={formData.returnPolicy}
            onChange={handleInputChange}
            multiline
            rows={2}
            error={errors.returnPolicy}
          />
        </div>
      </div>

      {/* File Uploads Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">File Uploads</h2>

        {/* Image Files */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Images</h3>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <FileUpload
              name="renders"
              label="Renders"
              value={formData.renders}
              onChange={handleInputChange}
              category="renders"
              projectTitle={formData.projectTitle}
              helpText="Upload building render images (JPG, PNG, WebP)"
            />

            <FileUpload
              name="drawings"
              label="Drawings"
              value={formData.drawings}
              onChange={handleInputChange}
              category="drawings"
              projectTitle={formData.projectTitle}
              helpText="Upload architectural drawings (JPG, PNG, WebP)"
            />

            <FileUpload
              name="_360sImages"
              label="360° Images"
              value={formData._360sImages}
              onChange={handleInputChange}
              category="_360sImages"
              projectTitle={formData.projectTitle}
              helpText="Upload 360-degree view images"
            />
          </div>
        </div>

        {/* 3D Model Files */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-800 mb-4">3D Models (.glb)</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FileUpload
              name="modelsFiles"
              label="Model Files"
              value={formData.modelsFiles}
              onChange={handleInputChange}
              category="modelsFiles"
              projectTitle={formData.projectTitle}
              helpText="Upload 3D model files (.glb)"
            />

            <FileUpload
              name="hideLevel"
              label="Hide Level Files"
              value={formData.hideLevel}
              onChange={handleInputChange}
              category="hideLevel"
              projectTitle={formData.projectTitle}
              showPriority={true}
              helpText="Upload hide level files with priority (.glb)"
            />

            <FileUpload
              name="supportFiles"
              label="Support Files"
              value={formData.supportFiles}
              onChange={handleInputChange}
              category="supportFiles"
              projectTitle={formData.projectTitle}
              helpText="Upload support files (.glb)"
            />

            <FileUpload
              name="roomSnaps"
              label="Room Snaps"
              value={formData.roomSnaps}
              onChange={handleInputChange}
              category="roomSnaps"
              projectTitle={formData.projectTitle}
              helpText="Upload room snapshot files (.glb)"
            />
          </div>
        </div>

        {/* Document Files */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Documents</h3>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <FileUpload
              name="presentationDrawings"
              label="Presentation Drawings"
              value={formData.presentationDrawings}
              onChange={handleInputChange}
              category="presentationDrawings"
              projectTitle={formData.projectTitle}
              helpText="Upload presentation drawings (.pdf)"
            />

            <FileUpload
              name="constructionDrawingsPdf"
              label="Construction Drawings (PDF)"
              value={formData.constructionDrawingsPdf}
              onChange={handleInputChange}
              category="constructionDrawingsPdf"
              projectTitle={formData.projectTitle}
              helpText="Upload construction drawings (.pdf)"
            />

            <FileUpload
              name="constructionDrawingsDwg"
              label="Construction Drawings (DWG)"
              value={formData.constructionDrawingsDwg}
              onChange={handleInputChange}
              category="constructionDrawingsDwg"
              projectTitle={formData.projectTitle}
              helpText="Upload CAD drawings (.dwg)"
            />
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex justify-between items-center">
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={handleReset}
              disabled={isLoading}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
            >
              Reset
            </button>
            
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                disabled={isLoading}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
              >
                Cancel
              </button>
            )}
          </div>
          
          <button
            type="submit"
            disabled={isLoading || !isDirty}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Building' : 'Update Building'}
          </button>
        </div>
        
        {isDirty && (
          <p className="text-sm text-yellow-600 mt-2">
            You have unsaved changes
          </p>
        )}
      </div>
    </form>
  );
}
