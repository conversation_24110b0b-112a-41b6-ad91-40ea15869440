"use client";

import { useState } from "react";

export default function TestAuthPage() {
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const createTestUser = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/test-user", {
        method: "POST",
      });
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: error.message });
    }
    setLoading(false);
  };

  const checkTestUser = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/test-user");
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: error.message });
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold text-center mb-6">Test Authentication</h1>
        
        <div className="space-y-4">
          <button
            onClick={checkTestUser}
            disabled={loading}
            className="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            {loading ? "Checking..." : "Check Test User"}
          </button>
          
          <button
            onClick={createTestUser}
            disabled={loading}
            className="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
          >
            {loading ? "Creating..." : "Create Test User"}
          </button>
        </div>

        {result && (
          <div className="mt-6 p-4 bg-gray-100 rounded">
            <h3 className="font-bold mb-2">Result:</h3>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-100 rounded">
          <h3 className="font-bold mb-2">Test Credentials:</h3>
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Password:</strong> password123</p>
          <a 
            href="/auth/signin" 
            className="inline-block mt-2 text-blue-600 hover:underline"
          >
            Go to Sign In Page
          </a>
        </div>
      </div>
    </div>
  );
}
