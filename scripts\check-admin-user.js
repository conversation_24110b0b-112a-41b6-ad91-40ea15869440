// scripts/check-admin-user.js
// <PERSON>ript to check admin user details

import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const uri = process.env.MONGODB_URI;
const adminEmail = '<EMAIL>';

async function checkAdminUser() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    // Find admin user
    const adminUser = await usersCollection.findOne({ email: adminEmail });
    
    if (adminUser) {
      console.log('\n📋 Admin User Found:');
      console.log(`   ID: ${adminUser._id}`);
      console.log(`   Email: ${adminUser.email}`);
      console.log(`   Name: ${adminUser.name}`);
      console.log(`   Role: ${adminUser.role}`);
      console.log(`   Has Password: ${adminUser.password ? 'Yes' : 'No'}`);
      console.log(`   Password Length: ${adminUser.password ? adminUser.password.length : 'N/A'}`);
      console.log(`   Email Verified: ${adminUser.emailVerified ? 'Yes' : 'No'}`);
      console.log(`   Created: ${adminUser.createdAt}`);
      console.log(`   Updated: ${adminUser.updatedAt}`);
      
      if (adminUser.password) {
        console.log('\n✅ Admin user has password - credentials login should work');
      } else {
        console.log('\n❌ Admin user missing password - credentials login will fail');
      }
    } else {
      console.log('\n❌ Admin user not found');
    }
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error);
  } finally {
    await client.close();
  }
}

checkAdminUser();
