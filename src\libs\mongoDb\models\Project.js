import mongoose from 'mongoose';
const { Schema } = mongoose;

const projectSchema = new Schema({
    projectTitle:{type:String,required:true,unique:true},
    projectFiles:{type:Array,required:true,default:[
        {id:0,name:'360 1',url:'/360/0001.jpg'},
        {id:1,name:'360 2',url:'/360/0003.jpg'},
    ]},
},{timestamps:true});

export const Project = mongoose.models.Project||mongoose.model('Project', projectSchema)