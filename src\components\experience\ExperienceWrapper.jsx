'use client'

import React, { Suspense, useRef } from 'react'
// import ExperienceUi from './ExperienceUi'
// import ExperienceWorld from './ExperienceWorld'
import dynamic from 'next/dynamic'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { useLoader } from '@react-three/fiber'
import * as THREE from 'three' // Import THREE for useLoader
import ErrorBoundary from '../ErrorBoundary'
import ClientOnly from '../ClientOnly'

const ExperienceWorld = dynamic(() => import('./ExperienceWorld'),{ssr:false})
const ExperienceUi = dynamic(() => import('./ExperienceUi'),{ssr:false})

// Dynamically import Preloader to ensure it only runs on the client
const Preloader = dynamic(
  () => import('./Preloader'), // Create a new file for Preloader
  { ssr: false }
);


export default function ExperienceWrapper({data}) {
  const {experienceState}=useExperienceContext()
  
  // Determine the URL for the default 360 image
  // Make sure data and _360sImages are available here to get the URL
  const default360ImageUrl = data?._360sImages?.[0]?.url;

  // Early return if no data is provided
  if (!data) {
    // console.warn('ExperienceWrapper: No data provided');
    return (
      <div className='flex relative w-full h-full items-center justify-center'>
        <div className="text-gray-500">Loading experience...</div>
      </div>
    );
  }

  return (
    <div className='flex relative w-full h-full'>
      <ClientOnly fallback={
        <div className="absolute top-4 left-4 z-50 text-white bg-black/50 p-2 rounded">
          Loading UI...
        </div>
      }>
        <ErrorBoundary fallbackMessage="Experience UI failed to load. Please refresh the page.">
          <ExperienceUi data={data}/>
        </ErrorBoundary>
      </ClientOnly>
      <Suspense fallback={
        <div className='flex items-center justify-center w-full h-full'>
          <div className="text-gray-500">Loading 3D experience...</div>
        </div>
      }>
        <ErrorBoundary fallbackMessage="3D Experience failed to load. Please refresh the page.">
          {/* Preload the default texture here */}
          {default360ImageUrl && <Preloader initial360ImageUrl={default360ImageUrl} />}
          {/* Render the ExperienceWorld component */}
          <ExperienceWorld data={data}/>
        </ErrorBoundary>
      </Suspense>
    </div>
  )
}
