import { buildings } from '@/libs/blg'
import React, { Suspense } from 'react'
import BuildPageComponent from '@/components/BuildPageComponent'
import Scroller<PERSON>rapper from '@/components/ScrollerWrapper'
import Image from 'next/image'
import LoadingComponent from '@/components/LoadingComponent'
import ExperienceWrapper from '@/components/experience/ExperienceWrapper'
import { settings } from '@/libs/siteSettings'

export default async function page({params}) {
  const cssSection='sectionWrapper flex w-full h-full flex-none relative overflow-hidden'
  const {id}=await params
  let data = null
  try {
    const response = await fetch(`${settings.url}/api/buildings/${id}`)
    
    if (!response.ok) {
      // If the server responded with a non-2xx status code
      console.error(`HTTP error! status: ${response.status}`);
      // You might want to throw an error, redirect, or return a specific UI
      // For now, let's just return an empty data set or handle it
      return <div>Error loading product: {response.statusText}</div>;
    }
    
    if (!response.ok) {
      throw new Error('Failed to fetch building');
    }

    const {building} = await response.json();    
    data=building

  } catch (error) {
    console.error('Failed to fetch product data:', error);
    // Handle network errors or other issues during fetch
    return <div className='absolute w-full h-full m-auto'>Failed to load product data due to a network error.</div>;
  }

  if (!data) {
    // Handle cases where data is null after fetch attempts
    return <div className='absolute w-full h-full  m-auto'>Product not found or no data available.</div>;
  }

  // console.log('ProductPage:',data)
  return (
    <div className='flex w-full h-svh'>
      <BuildPageComponent data={data}>
        {/* renders wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {data?.renders?.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            )}
          </ScrollerWrapper>
        </section>
        
        {/* drawings wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {data?.drawings?.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            )}
          </ScrollerWrapper>
        </section>

        {/* experience wrapper */}
        <section className={cssSection}>
          <Suspense fallback={<LoadingComponent/>}>
            <ExperienceWrapper data={data}/>
          </Suspense>
        </section>
      </BuildPageComponent>
    </div>
  )
}
