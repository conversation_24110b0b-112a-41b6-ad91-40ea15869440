// lib/db.js
// This file simulates a database for demonstration purposes.
// In a production application, you would replace this with a real database
// (e.g., MongoDB, PostgreSQL, MySQL) and a proper ORM/ODM (e.g., Prisma, Mongoose, Sequelize).

const users = []; // Stores user objects for credentials provider
const verificationTokens = []; // Stores tokens for email verification (magic link)
const passwordResetTokens = []; // Stores tokens for password reset

/**
 * Finds a user by email in the in-memory store.
 * @param {string} email
 * @returns {object|undefined} The user object if found, otherwise undefined.
 */
function getUserByEmail(email) {
  return users.find(user => user.email === email);
}

/**
 * Creates a new user in the in-memory store.
 * @param {object} user - The user object to create (e.g., { id, email, password, name }).
 * @returns {object} The created user object.
 */
function createUser(user) {
  users.push(user);
  return user;
}

/**
 * Updates an existing user in the in-memory store.
 * @param {string} id - The ID of the user to update.
 * @param {object} updates - An object containing the fields to update.
 * @returns {object|undefined} The updated user object if found, otherwise undefined.
 */
function updateUser(id, updates) {
  const userIndex = users.findIndex(user => user.id === id);
  if (userIndex > -1) {
    users[userIndex] = { ...users[userIndex], ...updates };
    return users[userIndex];
  }
  return undefined;
}

/**
 * Finds a verification token by token string.
 * @param {string} token
 * @returns {object|undefined} The token object if found, otherwise undefined.
 */
function findVerificationToken(token) {
  return verificationTokens.find(vt => vt.token === token);
}

/**
 * Creates a new verification token.
 * @param {object} token - The token object (e.g., { identifier, token, expires }).
 */
function createVerificationToken(token) {
  // In a real database, you'd save this to a 'verificationToken' collection.
  // Ensure existing tokens for the same identifier are invalidated/deleted.
  const existingIndex = verificationTokens.findIndex(vt => vt.identifier === token.identifier);
  if (existingIndex > -1) {
    verificationTokens.splice(existingIndex, 1);
  }
  verificationTokens.push(token);
}

/**
 * Deletes a verification token.
 * @param {string} token - The token string to delete.
 */
function deleteVerificationToken(token) {
  const index = verificationTokens.findIndex(vt => vt.token === token);
  if (index > -1) {
    verificationTokens.splice(index, 1);
  }
}

/**
 * Finds a password reset token by token string.
 * @param {string} token - The password reset token string.
 * @returns {object|undefined} The token object if found, otherwise undefined.
 */
function findPasswordResetToken(token) {
  return passwordResetTokens.find(prt => prt.token === token);
}

/**
 * Creates a new password reset token.
 * @param {object} token - The token object (e.g., { email, token, expires }).
 */
function createPasswordResetToken(token) {
  // In a real database, invalidate/delete any existing tokens for the same email.
  const existingIndex = passwordResetTokens.findIndex(prt => prt.email === token.email);
  if (existingIndex > -1) {
    passwordResetTokens.splice(existingIndex, 1);
  }
  passwordResetTokens.push(token);
}

/**
 * Deletes a password reset token.
 * @param {string} token - The password reset token string to delete.
 */
function deletePasswordResetToken(token) {
  const index = passwordResetTokens.findIndex(prt => prt.token === token);
  if (index > -1) {
    passwordResetTokens.splice(index, 1);
  }
}

/**
 * Finds a user by ID in the in-memory store.
 * This is used by NextAuth's adapter functions.
 * @param {string} id
 * @returns {object|undefined} The user object if found, otherwise undefined.
 */
function getUserById(id) {
  return users.find(user => user.id === id);
}


// --- Adapters for NextAuth.js (simplified in-memory version) ---
// These functions mock the interface for a database adapter
// used by NextAuth.js, especially for the Email provider.

const adapter = {
  async createUser(data) {
    const newUser = { id: crypto.randomUUID(), ...data };
    users.push(newUser);
    return newUser;
  },
  async getUser(id) {
    return getUserById(id);
  },
  async getUserByEmail(email) {
    return getUserByEmail(email);
  },
  async getUserByAccount({ providerAccountId, provider }) {
    // In a real adapter, you'd look up accounts linked to users.
    // For this in-memory mock, we'll assume a direct user find by email if needed,
    // or you'd need to store accounts array on users for social logins.
    // This part is more complex for a simple in-memory mock without full account linking.
    // For now, we'll return undefined, as social accounts aren't fully persisted here.
    console.warn("Adapter getUserByAccount not fully implemented for in-memory mock.");
    return null;
  },
  async updateUser(user) {
    const existingUserIndex = users.findIndex(u => u.id === user.id);
    if (existingUserIndex > -1) {
      users[existingUserIndex] = { ...users[existingUserIndex], ...user };
      return users[existingUserIndex];
    }
    return null;
  },
  async deleteUser(userId) {
    const index = users.findIndex(u => u.id === userId);
    if (index > -1) {
      users.splice(index, 1);
    }
    return null; // Or the deleted user object
  },
  async linkAccount(account) {
    // In a real adapter, you'd link an OAuth account to a user.
    // For this mock, we're not fully tracking linked accounts.
    console.warn("Adapter linkAccount not fully implemented for in-memory mock.");
    return account;
  },
  async unlinkAccount({ providerAccountId, provider }) {
    console.warn("Adapter unlinkAccount not fully implemented for in-memory mock.");
  },
  async createSession(session) {
    // Sessions are typically handled by NextAuth.js using JWT strategy or the database adapter.
    // When using JWT strategy, this isn't directly used for server-side sessions.
    console.warn("Adapter createSession not fully implemented for in-memory mock (using JWT).");
    return session;
  },
  async getSessionAndUser(sessionToken) {
    console.warn("Adapter getSessionAndUser not fully implemented for in-memory mock (using JWT).");
    return null;
  },
  async updateSession(session) {
    console.warn("Adapter updateSession not fully implemented for in-memory mock (using JWT).");
    return session;
  },
  async deleteSession(sessionToken) {
    console.warn("Adapter deleteSession not fully implemented for in-memory mock (using JWT).");
  },
  async createVerificationToken(token) {
    // Store the token and return it.
    createVerificationToken(token);
    return token;
  },
  async useVerificationToken({ identifier, token }) {
    const foundToken = verificationTokens.find(
      vt => vt.identifier === identifier && vt.token === token
    );
    if (foundToken && foundToken.expires > new Date()) {
      deleteVerificationToken(token); // Invalidate token after use
      return foundToken;
    }
    return null;
  },
};

export {
  users,
  verificationTokens,
  passwordResetTokens,
  getUserByEmail,
  createUser,
  updateUser,
  findPasswordResetToken,
  createPasswordResetToken,
  deletePasswordResetToken,
  adapter, // Export the mock adapter for NextAuth.js
};
