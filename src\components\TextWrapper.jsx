'use client'

import Link from 'next/link'
import React, { useState } from 'react'
import { IoCarOutline } from "react-icons/io5";
import { IoBedOutline } from "react-icons/io5";
import { TbArrowAutofitHeight } from "react-icons/tb";
import { SiLevelsdotfyi } from "react-icons/si";
import { LuBath } from "react-icons/lu";
import { TbArrowAutofitWidth } from "react-icons/tb";
import { MdClose } from 'react-icons/md';

export default function TextWrapper({data = {}}) {
   const [formData, setFormData] = useState({
    firstname: '',
    surname: '',
    email: '',
    number: '',
    message: '',
  });
  const [loading, setLoading] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [showMessgaeInput,setShowMessgaeInput]=useState(false)
  const css = {
    icons:'w-5 h-5',
    title:'text-xs xl:text-sm capitalize font-medium',
    desc:'text-xs xl:text-sm capitalize font-medium',
    button:'flex items-center justify-center font-bold text-xs capitalize h-10 rounded w-full bg-slate-100 shadow',
    divContainer:'flex flex-col gap-0 text-xs w-fit h-fit justify-center items-center p-2',
    text:'flex w-full h-10 flex-none px-2 rounded shadow bg-slate-200 shadow-md placeholder:text-xs',
    textArea:'flex w-full rounded px-2 shadow bg-slate-200 shadow-md placeholder:text-xs',
  }
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const inputsFields=[
    {
      // onChange:handleChange,
      name:'firstname',type:'text',placeholder:'name'},
    {
      // onChange:handleChange,
      name:'surname',type:'text',placeholder:'surname'},
    {
      // onChange:handleChange,
      name:'email',type:'email',placeholder:'email'},
    {
      // onChange:handleChange,
      name:'number',type:'tel',placeholder:'phone'},
  ]

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setSubmitMessage('');
    setShowMessgaeInput(false)
    // console.log(formData)
    // console.log(`${settings.url}/api/send-email`)

    try {
      // The API route will be at /api/send-email in your Next.js app
      const response = await fetch(`/api/send-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setSubmitMessage('Message sent successfully!');
        setFormData({
          firstname: '',
          surname: '',
          email: '',
          number: '',
          message: '',
        }); // Clear form
      } else {
        const errorData = await response.json();
        setSubmitMessage(`Failed to send message: ${errorData.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setSubmitMessage('An error occurred while sending your message.');
    } finally {
      setLoading(false);
    }
  };

  // console.log('TextWrapper:',data)
  return (
    <div className='textwrapper select-none flex relative flex-col gap-0 lg:top-20 top-0 h-full lg:h-[calc(100%-80px)] overflow-hidden z-0'>
      <div className='flex text-gray-500 flex-col w-full h-fit gap-2 mb-11 px-4 md:mb-2 overflow-y-auto overflow-x-hidden'>
        {data?.buildingSummary && (
          <div className='flex sticky top-0 left-0 rounded-md shadow-md items-center justify-around bg-slate-100 w-full h-20'>
            <div className={css.divContainer}>
              <p className={css.title}>length</p>
              <p className={css.desc}>{data.buildingSummary.length || '-'}</p>
              <TbArrowAutofitWidth className={css.icons} />
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>width</p>
              <p className={css.desc}>{data.buildingSummary.width || '-'}</p>
              <TbArrowAutofitHeight className={css.icons} />
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>beds</p>
              <p className={css.desc}>{data.buildingSummary.beds || '-'}</p>
              <IoBedOutline className={css.icons}/>
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>levels</p>
              <p className={css.desc}>{data.buildingSummary.levels || '-'}</p>
              <SiLevelsdotfyi className={css.icons}/>
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>baths</p>
              <p className={css.desc}>{data.buildingSummary.baths || '-'}</p>
              <LuBath className={css.icons}/>
            </div>
            <div className={css.divContainer}>
              <p className={css.title}>cars</p>
              <p className={css.desc}>{data.buildingSummary.cars || '-'}</p>
              <IoCarOutline className={css.icons}/>
            </div>
          </div>
        )}
        <h1 className='md:text-5xl text-2xl font-bold capitalize h-fit md:my-3 tracking-tight leading-10'>{data?.buildingTitle || 'Building'}</h1>
        {data?.buildingType && (
          <Link href={`/buildings?collection=${data.buildingType}`} className='text-sm md:py-1 capitalize underline cursor-pointer'>
            {data.buildingType}
          </Link>
        )}
        {data?.desc && <p className='text-sm leading-5 text-justify font-light'>{data.desc}</p>}
        {data?.features && <p className='text-sm font-medium leading-5 text-justify'>{data.features}</p>}
        <div className='flex gap-2 w-full flex-col px-1'>
          <hr className='border-1 border-gray-300'/>
          <div className='flex flex-col gap-1 w-full'>
            {Array.isArray(data?.buildingHighlights) && data.buildingHighlights.map((i,index)=>
              (<div key={index} className='flex w-full flex-col'>
                <h1 className='text-xs font-semibold justify-center text-center underline'>{i?.title || ''}</h1>
                <p className='text-xs font-light justify-center text-center'>{i?.description || ''}</p>
              </div>)
            )}
          </div>
          <hr className='border-1 border-gray-300'/>
        </div>
        {data?.outroSection && <p className='text-sm font-light leading-5 text-justify'>{data.outroSection}</p>}
        <section id='contact' className='flex flex-col w-full h-fit rounded-md py-1 mb-1'>
          <div className='flex flex-col w-full h-fit gap-2 bg-slate-200 rounded-t-2xl p-2 shadow-md'>
            {['download brochure','download drawings'].map((i,index)=>
              <button key={index} className='flex items-center justify-center flex-col w-full min-h-12 bg-gray-100 rounded-lg shadow cursor-pointer underline capitalize font-bold text-xs'>
                {i}
              </button>
            )}
          </div>
          <form
            className='flex flex-col w-full h-fit gap-2 mb-4 lg:mb-14 bg-slate-500 rounded-b-2xl p-2 shadow-md'
            // onSubmit={handleSubmit}
          >
            {inputsFields.map(i=>
              <input
                key={i?.name}
                className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
                {...i}
                onChange={(e)=>handleChange(e)}
              />
            )}
            <textarea
              placeholder='message'
              name='message'
              className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
              rows={5}
              onChange={(e)=>handleChange(e)}
            />
            <input onClick={handleSubmit} type="button" value="send message" className='flex items-center justify-center w-full min-h-10 rounded-md bg-slate-800 cursor-pointer outline-none hover:bg-slate-700 duration-300 ease-linear text-sm capitalize text-white'/>
          </form>
        </section>
      </div>
      <div className='flex absolute bottom-0 bg-gray-50 w-full h-fit p-2 rounded-lg shadow-md'>
        <button
          type="button"
          onClick={() => setShowMessgaeInput(true)}
          className='flex items-center text-white capitalize justify-center rounded-md bg-gray-800 w-full min-h-12'
        >
          contact
        </button>
      </div>
      {
        showMessgaeInput && <div className='flex absolute m-auto z-20 w-full h-full bg-black/75 items-center justify-center p-4'>
          <form
            className='flex relative flex-col w-full h-fit gap-2 mb-14 bg-slate-500 rounded-b-2xl p-2 shadow-md'
            // onSubmit={handleSubmit}
          >
            <MdClose
              type="button"
              onClick={() => setShowMessgaeInput(false)}
              className='flex absolute -top-14 right-2 items-center text-white capitalize justify-center rounded-full bg-gray-800 w-fit min-h-12 p-1'
            />
            {inputsFields.map(i=>
              <input
                key={i?.name}
                className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
                {...i}
                onChange={(e)=>handleChange(e)}
              />
            )}
            <textarea
              placeholder='message'
              name='message'
              className='flex items-center bg-gray-100 shadow outline-none overflow-hidden text-xs p-2 placeholder:text-xs justify-center w-full h-10 rounded-lg'
              rows={5}
              onChange={(e)=>handleChange(e)}
            />
            <button onClick={handleSubmit} type="button" value="send message" className='flex items-center justify-center w-full min-h-10 rounded-md bg-slate-800 cursor-pointer outline-none hover:bg-slate-700 duration-300 ease-linear text-sm capitalize text-white'>submit</button>
          </form>
        </div>
      }
    </div>
  )
}
