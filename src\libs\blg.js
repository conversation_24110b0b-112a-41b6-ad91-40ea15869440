export const buildings=[
  {
    _id: 0,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
  {
    _id: 1,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
  {
    _id: 2,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
  {
    _id: 3,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
  {
    _id: 4,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
  {
    _id: 5,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
  {
    _id: 6,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
  {
    _id: 7,
    projectTitle: "mr mudenda",
    price: "400",
    buildingTitle: "4 bedroomed house",
    buildingType: "multi-Storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingSummary: {
      width: "13",
      length: "16",
      levels: "2",
      beds: "4",
      baths: "3",
      cars: "2"
    },
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        "id": 1,
        "name": "0001",
        "url": "/buildings/GNorth/View 1.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 2,
        "name": "0002",
        "url": "/buildings/GNorth/View 2.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
      {
        "id": 3,
        "name": "0003",
        "url": "/buildings/GNorth/View 3.jpg",
        "path": "luyaridesigns/ms_mudenda-0007.jpg"
      },
    ],
    drawings: [
      {
        "id": 1,
        "name": "Elevations",
        "url": "/buildings/GNorth/Elevations.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
      {
        "id": 1,
        "name": "Floor Plan",
        "url": "/buildings/GNorth/floor-plans.jpg",
        "path": "luyaridesigns/ms_mudenda-Option-1.jpg"
      },
    ],
    modelsFiles: [
      {
        "id": 1,
        "name": "grd floor",
        "url": "/buildings/GNorth/house_Opt_grd.glb",
        "path": "luyaridesigns/ms_mudenda-grd floor.glb"
      }
    ],
    hideLevel: [
      {
        "id": 1,
        "name": "roof",
        "url": "/buildings/GNorth/house_Opt_roof.glb",
        "path": "luyaridesigns/ms_mudenda-roof.glb"
      },
    ],
    supportFiles: [
      {
        "id": 1,
        "name": "Site",
        "url": "/buildings/GNorth/house_Opt_site.glb",
        "path": "luyaridesigns/ms_mudenda-Site.glb"
      }
    ],
    _360sImages: [
      {
        "id": 1,
        "name": "Opt1_360_0001",
        "url": "/buildings/GNorth/360 front view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
      {
        "id": 0,
        "name": "Opt1_360_0002",
        "url": "/buildings/GNorth/360 pool view.jpg",
        "path": "luyaridesigns/ms_mudenda-Opt1_360_0001.jpg"
      },
    ],
    roomSnaps: [],
    presentationDrawings: [],
    constructionDrawingsPdf: [],
    constructionDrawingsDwg: [],
    tags: [
      ""
    ],
    color: [],
    collections: [
      "hero"
    ],
    coments: [],
    position: "-8,0,6",
    arPosition: "0,0,0",
    minDistance: "40",
    maxDistance: "80",
    createdAt:"2025-01-27T07:53:37.368Z",
    updatedAt: "2025-02-19T11:19:48.301Z",
    likes: [
      "<EMAIL>"
    ],
    buildingRole: "exhibit"
  },
]