'use client'
import React, { useRef, useEffect, useState, useCallback } from 'react'
import ExperienceModelAR from './ExperienceModelAR'
import ARSessionManager from './ARSessionManager'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'

export default function ExperienceAR({data, onARSessionEnd}) { // Accept onARSessionEnd
  const refModelAR=useRef(null) // Ref for ExperienceModelAR component
  const refReticle=useRef(null) // Ref for reticle mesh object
  const [modelVisible, setModelVisible]=useState(false) // Controls if ExperienceModelAR is rendered
  const [modelPlacementMatrix, setModelPlacementMatrix]=useState(null) // Stores the matrix for model placement
  const [reticleVisible, setReticleVisible]=useState(false)

  useEffect(() => {
    console.log('ExperienceAR: Component mounted. AR session lifecycle delegated to ARSessionManager.')

    // Reset model visibility and placement matrix on mount/remount
    setModelVisible(false);
    setModelPlacementMatrix(null);
    setReticleVisible(false);

    return () => {
      console.log('ExperienceAR: Component unmounting.')
    }
  }, [])

  const handleModelSelect = useCallback((isVisible, matrixElements) => {
    console.log(`ExperienceAR: handleModelSelect called. isVisible: ${isVisible}`);
    setModelVisible(isVisible);
    if (matrixElements) {
      setModelPlacementMatrix(matrixElements);
    }
  }, []); // No dependencies as setter functions from useState are stable.

  return (
    <>
      <ARSessionManager
        onModelSelect={handleModelSelect}
        setReticleVisible={setReticleVisible}
        refReticle={refReticle}
        onARSessionEnd={onARSessionEnd} // Pass the handler down to ARSessionManager
      />
      <mesh
        ref={refReticle}
        visible={reticleVisible && !modelVisible} // Hide reticle once model is placed
        matrixAutoUpdate={false} // Important to prevent Three.js from overriding the matrix
      >
        <ringGeometry args={[0.1, 0.15, 32]} />
        <meshBasicMaterial
          color="#00ff00"
          transparent={true}
          opacity={0.9}
          side={THREE.DoubleSide}
          depthTest={false}
          depthWrite={false}
        />
      </mesh>
      {/* Conditionally render the model. When modelVisible becomes true, this will mount and be placed. */}
      {modelVisible && modelPlacementMatrix &&
        <ExperienceModelAR data={data} refModelAR={refModelAR} modelPlacementMatrix={modelPlacementMatrix} />}
    </>
  )
}