// ExperienceAR.jsx
'use client'
import React, { useRef, useEffect, useState } from 'react'
import ExperienceModelAR from './ExperienceModelAR'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'
import { useHitTest, useController, XRController } from '@react-three/xr' // Import XR hooks and components
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext' // Assuming you need this for AR scale

export default function ExperienceAR({data}) {
  const {experienceDispatch} = useExperienceContext(); // To potentially dispatch AR end actions
  const refModelAR = useRef(null); // Ref for ExperienceModelAR component
  const refReticle = useRef(null); // Ref for reticle mesh object
  const [modelPlaced, setModelPlaced] = useState(false); // Tracks if the model has been placed
  const [modelPlacementMatrix, setModelPlacementMatrix] = useState(null); // Stores the matrix for model placement

  // This useEffect will run when ExperienceAR mounts within ARCanvas
  useEffect(() => {
    console.log('ExperienceAR: Component mounted in ARCanvas. Initializing state.');
    setModelPlaced(false);
    setModelPlacementMatrix(null);
    // Reticle visibility will be handled by useHitTest
  }, []);

  // useHitTest hook for surface detection
  // Callback receives hit matrix and hit test source
  useHitTest((hitMatrix, hit) => {
    if (!modelPlaced && refReticle.current) {
      // Update reticle position
      refReticle.current.matrix.fromArray(hitMatrix);
      refReticle.current.visible = true;
    }
  }, {
    // Optional: Configure hit test options (e.g., specific plane types)
    // hitTestOptions: { planeTypes: ['horizontal'] }
  });

  // useController hook for controller input (select events)
  const leftController = useController('left');
  const rightController = useController('right');

  useEffect(() => {
    const onSelect = (e) => {
      console.log('ExperienceAR: Controller select event detected!');
      // Only place the model if it hasn't been placed yet
      if (!modelPlaced && refReticle.current && refReticle.current.visible) {
        const currentMatrix = refReticle.current.matrix.elements;
        setModelPlacementMatrix(currentMatrix);
        setModelPlaced(true); // Mark model as placed
        refReticle.current.visible = false; // Hide reticle after placement
        console.log('ExperienceAR: Model placed at reticle location.');
      }
    };

    // Attach event listeners to controllers
    if (leftController) {
      leftController.controller.addEventListener('select', onSelect);
      console.log('ExperienceAR: Left controller listener added.');
    }
    if (rightController) {
      rightController.controller.addEventListener('select', onSelect);
      console.log('ExperienceAR: Right controller listener added.');
    }

    return () => {
      // Clean up event listeners on unmount
      if (leftController) {
        leftController.controller.removeEventListener('select', onSelect);
        console.log('ExperienceAR: Left controller listener removed.');
      }
      if (rightController) {
        rightController.controller.removeEventListener('select', onSelect);
        console.log('ExperienceAR: Right controller listener removed.');
      }
    };
  }, [leftController, rightController, modelPlaced]); // Add modelPlaced to dependencies to re-attach listeners when state changes

  return (
    <>
      {/* Reticle: Remains visible until model is placed */}
      {!modelPlaced && (
        <mesh
          ref={refReticle}
          visible={false} // Initially hidden, made visible by useHitTest
          matrixAutoUpdate={false}
        >
          <ringGeometry args={[0.08, 0.12, 32]} /> {/* Slightly smaller for better precision */}
          <meshBasicMaterial
            color="#00ff00"
            transparent={true}
            opacity={0.9}
            side={THREE.DoubleSide}
            depthTest={false}
            depthWrite={false}
          />
        </mesh>
      )}

      {/* Render the model only when it's placed */}
      {modelPlaced && modelPlacementMatrix && (
        <ExperienceModelAR
          data={data}
          refModelAR={refModelAR}
          modelPlacementMatrix={modelPlacementMatrix}
        />
      )}

      {/* Add default controllers (optional, but good for visual feedback) */}
      <XRController />
      {/* <XRController right /> */} {/* If you want to explicitly add right controller */}
    </>
  );
}