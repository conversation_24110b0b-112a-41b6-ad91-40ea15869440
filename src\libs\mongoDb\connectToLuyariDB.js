// import mongoose from "mongoose"
// export const connectToDbLuyari = async () => {
//     try {
//         let db={}
//         if(db.readyState==1){
//             console.log('mongoDb already connected')
//         }else{
//             db=mongoose.connection.on('connected', () => console.log('connected to mongoDb'))
//             await mongoose.connect(process.env.MONGO_DB_LUYARI)
//         }
//     } catch (error) {
//         mongoose.connection.on('error',err=>{console.log(err)})
//         mongoose.connection.on('error',err=>{logError(err),console.log(err)})
//         handleError(error);
//         mongoose.connection.close()
//     }
// }
// lib/mongodb.ts
// lib/mongodb.js

import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  throw new Error(
    'Please define the MONGODB_URI environment variable inside .env.local'
  );
}

let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function dbConnect() {
  if (cached.conn) {
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    };

    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose;
    });
  }
  cached.conn = await cached.promise;
  return cached.conn;
}

export default dbConnect;
