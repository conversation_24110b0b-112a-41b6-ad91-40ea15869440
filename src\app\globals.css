@import "tailwindcss";

/* Font family configuration */
:root {
  --font-inter: 'Inter', system-ui, -apple-system, sans-serif;
  --font-jetbrains-mono: 'JetBrains Mono', 'Courier New', monospace;
}

/* Base font styles */
body {
  font-family: var(--font-inter);
}

.font-mono {
  font-family: var(--font-jetbrains-mono);
}

@keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
}

/* Apply the custom animation to the spinner class */
.spinner-tailwind {
  animation: spin 1s linear infinite;
}