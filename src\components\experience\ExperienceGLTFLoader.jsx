'use client'
import React from 'react'
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader'
import {DRACOLoader} from 'three/examples/jsm/loaders/DRACOLoader'
import { useLoader } from '@react-three/fiber'

export default function ExperienceGLTFLoader({path}) {
    // console.log('ExpWorldObj ExpWorldGltfLoader -',path)
    // useGLTF(url, '/draco-gltf')
    const model=useLoader(GLTFLoader, path?.url,(loader)=>{
        const dracoLoader=new DRACOLoader()
        dracoLoader.setDecoderPath('/draco/')
        loader.setDRACOLoader(dracoLoader)
        })
    // const model=useGLTF(path?.url)
    // console.log('ExpWorldObj ExpWorldGltfLoader -',model)
    return (
        <group name={path?.name}>
            <primitive object={model?.scene}/>
        </group>
    )
}
