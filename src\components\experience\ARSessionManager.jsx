'use client'
import React, { useRef, useEffect, useState } from 'react'
import { useThree, useFrame } from '@react-three/fiber'
import * as THREE from 'three'

export default function ARSessionManager({
  onModelSelect,
  setReticleVisible,
  refReticle,
  onARSessionEnd
}) {
  const refCurrentSession = useRef(null)
  const refHitTestSource = useRef(null)
  const controllerRef = useRef(null)
  const { gl, scene, camera } = useThree()
  
  const [sessionActive, setSessionActive] = useState(false)
  const [hitTestReady, setHitTestReady] = useState(false)

  const startAr = async () => {
    console.log('🚀 ARSessionManager: Attempting to start AR session...')
    try {
      if (!navigator.xr) {
        console.warn('❌ ARSessionManager: WebXR not supported on this browser.')
        onARSessionEnd?.()
        return
      }

      const isSupported = await navigator.xr.isSessionSupported('immersive-ar')
      if (!isSupported) {
        console.warn('❌ ARSessionManager: immersive-ar session not supported on this device.')
        onARSessionEnd?.()
        return
      }

      const session = await navigator.xr.requestSession('immersive-ar', {
        optionalFeatures: ['dom-overlay', 'hand-tracking', 'hit-test'],
        domOverlay: { root: document.body }
      })
      
      refCurrentSession.current = session
      
      gl.xr.enabled = true
      gl.xr.setReferenceSpaceType('local')
      await gl.xr.setSession(session)
      
      console.log('✅ ARSessionManager: AR session started successfully.')
      setSessionActive(true)

      // Set up controller
      const controller = gl.xr.getController(0)
      controllerRef.current = controller
      scene.add(controller)

      // Add controller event listeners
      controller.addEventListener('select', (event) => {
        console.log('👆 ARSessionManager: Controller select event detected.')
        handleControllerSelect(event)
      })

      // Set up session event listeners
      session.addEventListener('end', () => {
        console.log('🧹 ARSessionManager: AR session ended.')
        endAr()
      })

      // Set up hit testing when session starts
      gl.xr.addEventListener('sessionstart', async () => {
        console.log('XR session STARTED successfully (via listener). Setting up hit-testing.')
        try {
          const session = gl.xr.getSession()
          const viewerReferenceSpace = await session.requestReferenceSpace('viewer')
          const hitTestSource = await session.requestHitTestSource({ space: viewerReferenceSpace })
          
          refHitTestSource.current = hitTestSource
          setHitTestReady(true)
          setReticleVisible?.(true)
          
          console.log('✅ ARSessionManager: Hit test source ready')
          
        } catch (error) {
          console.error('❌ ARSessionManager: Failed to set up hit testing:', error)
        }
      })

      gl.xr.addEventListener('sessionend', () => {
        console.log('🧹 ARSessionManager: XR session ended (via listener).')
        endAr()
      })

    } catch (error) {
      console.error('❌ ARSessionManager: Failed to start AR session:', error)
      onARSessionEnd?.()
    }
  }

  const endAr = () => {
    console.log('🧹 ARSessionManager: Ending AR session...')
    
    setSessionActive(false)
    setHitTestReady(false)
    setReticleVisible?.(false)
    
    if (refCurrentSession.current) {
      try {
        if (!refCurrentSession.current.ended) {
          refCurrentSession.current.end()
        }
        
        gl.xr.enabled = false
        gl.xr.setSession(null)
        gl.setAnimationLoop(null)
        
        refCurrentSession.current = null
        refHitTestSource.current = null

        // Clean up controller
        if (controllerRef.current) {
          scene.remove(controllerRef.current)
          controllerRef.current = null
        }

        console.log('✅ ARSessionManager: AR session cleanup complete.')
        
      } catch (error) {
        console.error('❌ ARSessionManager: Error during session cleanup:', error)
      }
    }
    
    onARSessionEnd?.()
  }

  const handleControllerSelect = (event) => {
    if (!hitTestReady || !refHitTestSource.current || !refReticle.current) {
      console.log('❌ ARSessionManager: Hit test not ready or reticle not available')
      return
    }

    // Get the current hit test results
    const frame = gl.xr.getFrame()
    if (!frame) return

    const hitTestResults = frame.getHitTestResults(refHitTestSource.current)
    if (hitTestResults.length > 0) {
      const hit = hitTestResults[0]
      const pose = hit.getPose(gl.xr.getReferenceSpace())
      
      if (pose) {
        // Extract matrix elements for model placement
        const matrix = new THREE.Matrix4()
        matrix.fromArray(pose.transform.matrix)
        
        // Call the model select handler with visibility and matrix
        onModelSelect?.(true, pose.transform.matrix)
        
        console.log('✅ ARSessionManager: Model placed at hit test location')
      }
    }
  }

  // Hit test frame loop
  useFrame(() => {
    if (!sessionActive || !hitTestReady || !refHitTestSource.current || !refReticle.current) {
      return
    }

    const frame = gl.xr.getFrame()
    if (!frame) return

    const hitTestResults = frame.getHitTestResults(refHitTestSource.current)
    if (hitTestResults.length > 0) {
      const hit = hitTestResults[0]
      const pose = hit.getPose(gl.xr.getReferenceSpace())
      
      if (pose) {
        // Update reticle position
        const matrix = new THREE.Matrix4()
        matrix.fromArray(pose.transform.matrix)
        refReticle.current.matrix.copy(matrix)
        refReticle.current.visible = true
      }
    } else {
      refReticle.current.visible = false
    }
  })

  useEffect(() => {
    console.log('ARSessionManager useEffect: Component MOUNTED. Calling startAr().')
    startAr()

    return () => {
      console.log('ARSessionManager useEffect: Component UNMOUNTED. Calling endAr().')
      endAr()
    }
  }, [])

  // This component doesn't render anything visible - it just manages the AR session
  return null
}
