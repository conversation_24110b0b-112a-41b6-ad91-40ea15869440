'use client'
import React, { useRef, useEffect, useState } from 'react'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'; // Import Three.js

export default function ARSessionManager({ onModelSelect, onARSessionEnd, setReticleVisible, refReticle }) {
  const refCurrentHitPose = useRef(null); // To store the latest hit pose for model placement
  const refCurrentSession = useRef(null)
  const { gl, scene, camera } = useThree() // Destructure camera as well
  const controllerRef = useRef(null); // Ref to store the actual THREE.Group controller object

  const startAr = async () => {
    console.log('🚀 ARSessionManager: Attempting to start AR session...')
    try {
      if (!navigator.xr) {
        console.warn('❌ ARSessionManager: WebXR not supported on this browser.')
        console.log('Calling onARSessionEnd because WebXR is not supported.');
        onARSessionEnd(); // Ensure state is reset if AR not supported
        return
      }

      // Check if immersive-ar session is supported
      const isSupported = await navigator.xr.isSessionSupported('immersive-ar');
      if (!isSupported) {
        console.warn('❌ ARSessionManager: immersive-ar session not supported on this device.');
        console.log('Calling onARSessionEnd because immersive-ar is not supported.');
        onARSessionEnd(); // Ensure state is reset if AR not supported
        return;
      }

      // Try to request AR session with minimal features first
      let sessionConfig = {
        optionalFeatures: ['hit-test']
      }

      // Only add DOM overlay if we have a proper container
      const overlayContainer = document.getElementById('ar-overlay-container') || document.body
      if (overlayContainer) {
        sessionConfig.optionalFeatures.push('dom-overlay')
        sessionConfig.domOverlay = { root: overlayContainer }
      }

      console.log('🔧 ARSessionManager: Requesting session with config:', sessionConfig)
      const session = await navigator.xr.requestSession('immersive-ar', sessionConfig);
      refCurrentSession.current = session;

      gl.xr.enabled = true;

      // Set reference space type with safety check
      if (gl.xr.setReferenceSpaceType) {
        gl.xr.setReferenceSpaceType('local'); // 'local' for seated/standing, 'unbounded' for world tracking
      } else {
        console.warn('ARSessionManager: gl.xr.setReferenceSpaceType not available')
      }

      // Set the XR session with safety check
      if (gl.xr.setSession) {
        await gl.xr.setSession(session);
      } else {
        console.warn('ARSessionManager: gl.xr.setSession not available, storing session manually')
        gl.xr._session = session;
      }
      console.log('✅ ARSessionManager: AR session started successfully.');

      // Get the XR controller (e.g., the first one).
      // This returns a THREE.Group that automatically updates its position and orientation
      // based on the XR device's controller tracking.
      let controller = null;
      if (gl.xr.getController) {
        controller = gl.xr.getController(0);
      } else {
        console.warn('ARSessionManager: gl.xr.getController not available')
      }

      if (controller) {
        // If the controller group hasn't been added to the scene yet, add it
        if (!controllerRef.current) {
          // Add a visual representation to the controller's group for debugging/feedback
          const controllerVisual = new THREE.Mesh(
            new THREE.SphereGeometry(0.02, 8, 8), // Small sphere as visual
            new THREE.MeshBasicMaterial({ color: 0xff0000, wireframe: true }) // Red wireframe
          );
          controllerVisual.name = "XRControllerVisual";
          controller.add(controllerVisual); // Add the visual mesh as a child of the controller's Group

          scene.add(controller); // Add the controller's Group to the main scene
          controllerRef.current = controller; // Store the reference to the controller Group
          console.log('🎮 ARSessionManager: XR Controller (visual and group) added to scene.')
        }

        // Attach event listeners directly to the controller's Three.js object
        // These events are fired by the WebXRManager when the controller interacts
        controller.addEventListener('selectstart', () => {
          console.log('👆 ARSessionManager: Controller selectstart event detected.');
        });

        // The 'select' event is typically used for placing objects
        controller.addEventListener('select', () => {
          console.log('👆 ARSessionManager: Controller select event detected. Making model visible.');
          // When 'select' fires, we want to place the model at the last hit-test location.
          if (refCurrentHitPose.current) {
            console.log('Model will be placed at the last hit-test location.');
            onModelSelect(true, refCurrentHitPose.current.transform.matrix); // Pass matrix elements for placement
            setReticleVisible(false); // Hide reticle after placing
          }
        });

        controller.addEventListener('selectend', () => {
          console.log('👆 ARSessionManager: Controller selectend event detected.');
          // Removed onModelSelect(false) here, as the model should remain visible after placement
        });
        // Add other useful controller events for more robust interaction
        controller.addEventListener('squeezestart', () => console.log('🎮 Squeeze start'));
        controller.addEventListener('squeezeend', () => console.log('🎮 Squeeze end'));
        controller.addEventListener('connected', () => console.log('🎮 Controller connected'));
        controller.addEventListener('disconnected', () => console.log('🎮 Controller disconnected'));

        console.log('🎮 ARSessionManager: XR Controller event listeners added.')
      } else {
        console.warn('⚠️ ARSessionManager: No XR controller found initially. It might connect later during the session.');
      }

      gl.xr.addEventListener('sessionstart', async () => {
        console.log('XR session STARTED successfully (via listener). Setting up hit-testing and render loop.');
        const session = gl.xr.getSession();
        // Request a 'viewer' reference space to get the user's head pose for hit-testing
        const viewerReferenceSpace = await session.requestReferenceSpace('viewer');
        // Request a hit test source from the viewer's pose
        const hitTestSource = await session.requestHitTestSource({ space: viewerReferenceSpace });

        gl.setAnimationLoop((time, frame) => {
          if (frame) {
            const hitTestResults = frame.getHitTestResults(hitTestSource);
            if (hitTestResults.length > 0) {
              const hit = hitTestResults[0];
              // Get the pose in the 'local' reference space, which is typically the floor level
              const localReferenceSpace = gl.xr.getReferenceSpace();
              const hitPose = hit.getPose(localReferenceSpace);
              if (hitPose) {
                // Update reticle position using the hit pose's matrix
                refCurrentHitPose.current = hitPose; // Store the current hit pose
                // The reticle object itself (refReticle.current) must be a THREE.Object3D
                // and part of your scene graph.
                if (refReticle.current) {
                  const matrix = new THREE.Matrix4().fromArray(hitPose.transform.matrix);
                  refReticle.current.matrix.copy(matrix);
                  setReticleVisible(true); // Make reticle visible when a surface is found
                }
              } else {
                setReticleVisible(false); // Hide reticle if pose is not valid
              }
            } else {
              refCurrentHitPose.current = null; // No hit, so clear the stored pose
              setReticleVisible(false); // Hide reticle if no hit test results
            }
          }
          gl.render(scene, camera); // Render the scene on every XR frame
        });
      });

      gl.xr.addEventListener('sessionend', () => {
        console.log('--- XR session ENDED (via listener) ---');
        // This listener fires when the user exits AR from the browser's XR UI
        // or when refCurrentSession.current.end() is called.
        if (refCurrentSession.current && refCurrentSession.current.ended) {
          console.log('Session ended gracefully by component cleanup or user exit.');
        } else {
          console.error('Session ended unexpectedly! Check for external factors or errors.');
        }
        onARSessionEnd(); // Notify parent component to handle session end
      });

    } catch (error) {
      console.error('❌ ARSessionManager: Failed to start AR session:', error);
      console.log('Calling onARSessionEnd due to start failure.');
      // Ensure that if AR fails to start, the modeAR is also reset in the parent
      onARSessionEnd();
    }
  }

  const endAr = () => {
    console.log('🧹 ARSessionManager: Attempting to end AR session via endAr() function...')
    if (refCurrentSession.current) {
      try {
        // If the session hasn't already ended, explicitly end it.
        // This will trigger the 'sessionend' event listener.
        if (!refCurrentSession.current.ended) {
          refCurrentSession.current.end();
          console.log('AR session explicitly ended by endAr().');
        } else {
          console.log('AR session already ended, skipping explicit end call.');
        }
        
        gl.clear(); // Clear the WebGL canvas
        gl.xr.enabled = false; // Disable XR on the renderer

        // Clear the XR session with safety check
        if (gl.xr.setSession) {
          gl.xr.setSession(null); // Clear the XR session from the renderer
        } else {
          gl.xr._session = null;
        }

        gl.setAnimationLoop(null); // Stop the XR animation loop

        // Remove the controller object and its visual from the scene if they were added
        if (controllerRef.current) {
          controllerRef.current.remove(...controllerRef.current.children); // Remove children like the visual
          scene.remove(controllerRef.current);
          controllerRef.current = null; // Clear the ref
          console.log('🧹 ARSessionManager: Controller removed from scene and ref cleared.');
        }

        // Hide the model and reticle when the session is explicitly ended
        onModelSelect(false);
        setReticleVisible(false); // Ensure reticle is hidden on session end

        console.log('✅ ARSessionManager: AR session end initiated (via endAr function).');
      } catch (error) {
        console.error('❌ ARSessionManager: Error ending AR session:', error)
      }
    } else {
      console.log('ℹ️ ARSessionManager: No active session to end (endAr called but no current session).')
    }
  }

  useEffect(() => {
    console.log('ARSessionManager useEffect: Component MOUNTED. Calling startAr().');
    startAr();

    // Clean up function: end the AR session when the component unmounts
    return () => {
      console.log('ARSessionManager useEffect: Component UNMOUNTED. Calling endAr().');
      endAr(); // This cleanup runs when the component unmounts
    };
  }, []); // Empty dependency array ensures this effect runs once on mount and cleans up on unmount

  // This component primarily manages the AR session lifecycle and doesn't render
  // any visible elements directly into the Three.js scene itself,
  // beyond the temporary controller visual.
  return null;
}
