'use client'
import React, { useRef, useState, useEffect } from 'react'
import { BackSide } from 'three'
import * as THREE from 'three'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { degToRad } from 'three/src/math/MathUtils'

export default function Experience360({data}) {
  // console.log('ExperienceModel:',data?._360sImages)
  const {experienceState,experienceDispatch}=useExperienceContext()
  const [textureLoaded,setTextureLoaded]=useState(false)
  const [texture,setTexture]=useState(null)
  const [textureArray,setTextureArray]=useState([])
  const refLoaderManager=useRef(new THREE.LoadingManager())
  const refLoader=useRef(new THREE.TextureLoader(refLoaderManager.current))
  const refSphere=useRef(null)
  const {scene}=useThree()

  useEffect(() => {
    if (texture) {
      setTimeout(() => {
        setTextureLoaded(true)
      }, 1000)
    }
  }, [texture])
  
  useEffect(() => {
    setTextureArray(data?._360sImages)
  }, [])

  useEffect(() => {
    const textureMap=refLoader.current.load(textureArray?.[experienceState?.textureIndex || 0]?.url)
    setTexture(textureMap)
    // if(refLoader.current && refLoaderManager.current){
    //   refLoaderManager.current.onLoad(() => {
    //     setTextureLoaded(true)
    //   })
    // }
  }, [experienceState?.textureIndex, refLoaderManager.current, refLoader.current])
  
  // console.log('Experience360:',texture)
  
  return (
    <>
      {/* <mesh 
        ref={refSphere}
        name="Experience360"
        scale={[1,1,-1]}
        rotation-y={degToRad(90)}
      >
        <meshBasicMaterial 
          map={texture}
          side={BackSide}
        />
        <sphereGeometry args={[128,128,128]}/>
      </mesh> */}
      {textureLoaded && texture && <mesh 
        ref={refSphere}
        name="Experience360"
        scale={[1,1,-1]}
        rotation-y={degToRad(90)}
      >
        <meshBasicMaterial 
          map={texture}
          side={BackSide}
          args={[128,128,128]}
        />
        <sphereGeometry args={[128,128,128]}/>
      </mesh>}
    </>
  )
}
