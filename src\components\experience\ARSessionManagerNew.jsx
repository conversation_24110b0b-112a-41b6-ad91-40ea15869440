'use client'
import React, { useRef, useEffect, useState } from 'react'
import { useThree } from '@react-three/fiber'
import * as THREE from 'three'

export default function ARSessionManagerNew({
  onSessionStart,
  onSessionEnd,
  onHitTestReady,
  onControllerSelect,
  children
}) {
  const refCurrentSession = useRef(null)
  const refHitTestSource = useRef(null)
  const controllerRef = useRef(null)
  const { gl, scene, camera } = useThree()
  
  const [sessionData, setSessionData] = useState({
    isSessionActive: false,
    hitTestSource: null,
    hitTestReady: false,
    session: null
  })

  const startAr = async () => {
    console.log('🚀 ARSessionManagerNew: Attempting to start AR session...')
    console.log('🔍 ARSessionManagerNew: gl.xr available:', !!gl.xr)
    console.log('🔍 ARSessionManagerNew: gl.xr.enabled:', gl.xr?.enabled)

    try {
      if (!navigator.xr) {
        console.warn('❌ ARSessionManagerNew: WebXR not supported on this browser.')
        onSessionEnd?.()
        return
      }

      const isSupported = await navigator.xr.isSessionSupported('immersive-ar')
      if (!isSupported) {
        console.warn('❌ ARSessionManagerNew: immersive-ar session not supported on this device.')
        onSessionEnd?.()
        return
      }

      const session = await navigator.xr.requestSession('immersive-ar', {
        optionalFeatures: ['dom-overlay', 'hand-tracking', 'hit-test'],
        domOverlay: { root: document.body }
      })
      
      refCurrentSession.current = session
      
      gl.xr.enabled = true
      gl.xr.setReferenceSpaceType('local')
      await gl.xr.setSession(session)
      
      console.log('✅ ARSessionManagerNew: AR session started successfully.')

      // Set up controller
      const controller = gl.xr.getController(0)
      controllerRef.current = controller
      scene.add(controller)

      // Add controller event listeners
      controller.addEventListener('select', (event) => {
        console.log('👆 ARSessionManagerNew: Controller select event detected.')
        onControllerSelect?.(event, controller)
      })

      controller.addEventListener('selectstart', () => {
        console.log('👆 ARSessionManagerNew: Controller selectstart event detected.')
      })

      controller.addEventListener('selectend', () => {
        console.log('👆 ARSessionManagerNew: Controller selectend event detected.')
      })

      // Update session data
      setSessionData(prev => ({
        ...prev,
        isSessionActive: true,
        session: session,
        controller: controller
      }))

      onSessionStart?.(session)

      // Set up session event listeners
      session.addEventListener('end', () => {
        console.log('🧹 ARSessionManagerNew: AR session ended.')
        endAr()
      })

      // Set up hit testing when session starts
      gl.xr.addEventListener('sessionstart', async () => {
        console.log('XR session STARTED successfully (via listener). Setting up hit-testing.')
        try {
          const session = gl.xr.getSession()
          const viewerReferenceSpace = await session.requestReferenceSpace('viewer')
          const hitTestSource = await session.requestHitTestSource({ space: viewerReferenceSpace })
          
          refHitTestSource.current = hitTestSource
          
          // Update session data with hit test source
          setSessionData(prev => ({
            ...prev,
            hitTestSource: hitTestSource,
            hitTestReady: true
          }))
          
          onHitTestReady?.(hitTestSource)
          console.log('✅ ARSessionManagerNew: Hit test source ready')
          
        } catch (error) {
          console.error('❌ ARSessionManagerNew: Failed to set up hit testing:', error)
        }
      })

      gl.xr.addEventListener('sessionend', () => {
        console.log('🧹 ARSessionManagerNew: XR session ended (via listener).')
        endAr()
      })

    } catch (error) {
      console.error('❌ ARSessionManagerNew: Failed to start AR session:', error)
      onSessionEnd?.()
    }
  }

  const endAr = () => {
    console.log('🧹 ARSessionManagerNew: Ending AR session...')
    
    // Reset session data
    setSessionData({
      isSessionActive: false,
      hitTestSource: null,
      hitTestReady: false,
      session: null
    })
    
    if (refCurrentSession.current) {
      try {
        if (!refCurrentSession.current.ended) {
          refCurrentSession.current.end()
        }
        
        gl.xr.enabled = false
        gl.xr.setSession(null)
        gl.setAnimationLoop(null)
        
        refCurrentSession.current = null
        refHitTestSource.current = null

        // Clean up controller
        if (controllerRef.current) {
          scene.remove(controllerRef.current)
          controllerRef.current = null
        }

        console.log('✅ ARSessionManagerNew: AR session cleanup complete.')
        
      } catch (error) {
        console.error('❌ ARSessionManagerNew: Error during session cleanup:', error)
      }
    }
    
    onSessionEnd?.()
  }

  useEffect(() => {
    console.log('ARSessionManagerNew useEffect: Component MOUNTED.')

    // Add a small delay to ensure Canvas is properly initialized
    const timer = setTimeout(() => {
      console.log('ARSessionManagerNew: Starting AR session after Canvas initialization.')
      startAr()
    }, 100)

    return () => {
      console.log('ARSessionManagerNew useEffect: Component UNMOUNTED. Calling endAr().')
      clearTimeout(timer)
      endAr()
    }
  }, [])

  // Render children with session data
  return (
    <>
      {typeof children === 'function' ? children(sessionData) : children}
    </>
  )
}
