'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect, useState } from 'react'
import ExperienceARWrapper from './ExperienceARWrapper'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'

export default function ExperienceWorld({data}) {
  const {experienceState, experienceDispatch} = useExperienceContext() // Ensure setExperienceState is available from your context

  // Debug logging for mode states
  console.log('ExperienceWorld Debug:', {
    modeAR: experienceState?.modeAR,
    mode360: experienceState?.mode360,
    modeModel: experienceState?.modeModel,
    textureIndex: experienceState?.textureIndex,
    hasData: !!data
  })

  // Function to call when AR session ends
  const handleARSessionEnd = () => {
    // setExperienceState(prevState => ({
    //   ...prevState,
    //   modeAR: false, // Set modeAR to false to switch out of AR mode
    //   // Optionally, set a default view after exiting AR, e.g., enable model view
    //   // modeModel: true,
    //   // mode360: false,
    // }));
    experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_AR_OFF})
  };

  return (
    <CameraControlsErrorBoundary>
      <Canvas>
        <Suspense fallback={<LoadingSpinner/>}>
          {experienceState?.modeAR ? (
              <ExperienceARWrapper
                data={data}
                useNewARSystem={true}
                onARSessionEnd={handleARSessionEnd}
              />
            )
          : (
              <>
                <ExperienceOrbitControls data={data}/>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
              </>
            )
          }
        </Suspense>
      </Canvas>
    </CameraControlsErrorBoundary>
  )
}