'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect, useState } from 'react'
import ExperienceARWrapper from './ExperienceARWrapper'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'

export default function ExperienceWorld({data}) {
  const {experienceState, experienceDispatch} = useExperienceContext() // Ensure setExperienceState is available from your context

  // Debug logging for mode states
  console.log('ExperienceWorld Debug:', {
    modeAR: experienceState?.modeAR,
    mode360: experienceState?.mode360,
    modeModel: experienceState?.modeModel,
    textureIndex: experienceState?.textureIndex,
    hasData: !!data
  })

  // Function to call when AR session ends
  const handleARSessionEnd = () => {
    // setExperienceState(prevState => ({
    //   ...prevState,
    //   modeAR: false, // Set modeAR to false to switch out of AR mode
    //   // Optionally, set a default view after exiting AR, e.g., enable model view
    //   // modeModel: true,
    //   // mode360: false,
    // }));
    experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_AR_OFF})
  };

  return (
    <CameraControlsErrorBoundary>
      {experienceState?.modeAR ? (
        // AR Mode Canvas - with XR enabled
        <Canvas
          key="ar-canvas"
          gl={{
            xr: { enabled: true },
            antialias: true,
            alpha: true,
            preserveDrawingBuffer: true
          }}
          camera={{
            position: [0, 1.6, 3],
            fov: 75,
            near: 0.1,
            far: 1000
          }}
          onCreated={({ gl }) => {
            // Add safety checks for WebXR methods that might not exist
            if (gl.xr) {
              if (!gl.xr.getEnvironmentBlendMode) {
                gl.xr.getEnvironmentBlendMode = () => 'opaque'
              }
              if (!gl.xr.getController) {
                gl.xr.getController = () => null
              }
              if (!gl.xr.getControllerGrip) {
                gl.xr.getControllerGrip = () => null
              }
              if (!gl.xr.setReferenceSpaceType) {
                gl.xr.setReferenceSpaceType = () => console.log('setReferenceSpaceType polyfill called')
              }
              if (!gl.xr.setSession) {
                gl.xr.setSession = (session) => {
                  console.log('setSession polyfill called with session:', session)
                  // Store the session for later use
                  gl.xr._session = session
                  return Promise.resolve()
                }
              }
              if (!gl.xr.getSession) {
                gl.xr.getSession = () => gl.xr._session || null
              }
            }

            // Add WebGL context loss handling
            gl.domElement.addEventListener('webglcontextlost', (event) => {
              console.warn('⚠️ WebGL context lost:', event)
              event.preventDefault()
            })

            gl.domElement.addEventListener('webglcontextrestored', (event) => {
              console.log('✅ WebGL context restored:', event)
            })

            console.log('🎨 AR Canvas created with WebXR support and safety polyfills')
          }}
        >
          <Suspense fallback={<LoadingSpinner/>}>
            <ExperienceARWrapper
              data={data}
              useNewARSystem={true}
              onARSessionEnd={handleARSessionEnd}
            />
          </Suspense>
        </Canvas>
      ) : (
        // Regular Mode Canvas - without XR
        <Canvas
          key="regular-canvas"
          camera={{
            position: [0, 1.6, 3],
            fov: 75,
            near: 0.1,
            far: 1000
          }}
        >
          <Suspense fallback={<LoadingSpinner/>}>
            <ExperienceOrbitControls data={data}/>
            {experienceState?.mode360 && <Experience360 data={data}/>}
            {experienceState?.modeModel && <ExperienceModel data={data}/>}
          </Suspense>
        </Canvas>
      )}
    </CameraControlsErrorBoundary>
  )
}