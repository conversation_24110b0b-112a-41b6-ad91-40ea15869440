// ExperienceWorld.jsx
'use client'
import { Canvas } from '@react-three/fiber' // Will be replaced by ARCanvas for AR mode
import React, { Suspense, useRef, useEffect, useState } from 'react'
import ExperienceAR from './ExperienceAR' // This is now the entry point for AR
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'
import { ARCanvas } from '@react-three/xr' // Import ARCanvas

export default function ExperienceWorld({data}) {
  const {experienceState, experienceDispatch} = useExperienceContext()

  // The handleARSessionEnd function might not be directly called this way anymore
  // as ARCanvas manages the session. We'll handle session end via XR events in ExperienceAR.
  const handleARSessionEnd = () => {
    console.log("ARSession ended, dispatching ACTIONS_EXPERIENCE.MODE_AR_OFF");
    experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_AR_OFF});
  };

  console.log('ExperienceWorld: Rendering. modeAR:', experienceState?.modeAR);

  return (
    <CameraControlsErrorBoundary>
      {/* Conditionally render ARCanvas or standard Canvas */}
      {experienceState?.modeAR ? (
        <ARCanvas
          camera={{ position: [0, 0, 0] }} // AR camera starts at 0,0,0
          // onSessionEnd is a prop of ARCanvas
          onSessionEnd={handleARSessionEnd}
          // The 'session' event listener in ARSessionManager's logic handled by ARCanvas
          // 'ray' is good for hit-testing performance
          raycaster={{type: 'webxr'}} 
          // Optional: enable dom-overlay
          // domOverlay={{ root: document.body }}
        >
          <Suspense fallback={<LoadingSpinner/>}>
            <ExperienceAR data={data} /> {/* ExperienceAR now lives inside ARCanvas */}
          </Suspense>
        </ARCanvas>
      ) : (
        <Canvas>
          <Suspense fallback={<LoadingSpinner/>}>
            <ExperienceOrbitControls data={data}/>
            {experienceState?.mode360 && <Experience360 data={data}/>}
            {experienceState?.modeModel && <ExperienceModel data={data}/>}
          </Suspense>
        </Canvas>
      )}
    </CameraControlsErrorBoundary>
  )
}