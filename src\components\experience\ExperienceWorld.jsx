'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect, useState } from 'react'
import * as THREE from 'three'
import ExperienceARWrapper from './ExperienceARWrapper'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'

const DEBUG_AR_POLYFILLS = false; // Set to true to see polyfill warnings, false to suppress

export default function ExperienceWorld({data}) {
  const {experienceState, experienceDispatch} = useExperienceContext() // Ensure setExperienceState is available from your context

  // Debug logging for mode states
  console.log('ExperienceWorld Debug:', {
    modeAR: experienceState?.modeAR,
    mode360: experienceState?.mode360,
    modeModel: experienceState?.modeModel,
    textureIndex: experienceState?.textureIndex,
    hasData: !!data
  })

  // Function to call when AR session ends
  const handleARSessionEnd = () => {
    // setExperienceState(prevState => ({
    //   ...prevState,
    //   modeAR: false, // Set modeAR to false to switch out of AR mode
    //   // Optionally, set a default view after exiting AR, e.g., enable model view
    //   // modeModel: true,
    //   // mode360: false,
    // }));
    experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_AR_OFF})
  };

  return (
    <>
      {experienceState?.modeAR ? (
        // AR Mode Canvas - with XR enabled (no camera controls)
        <Canvas
          key="ar-canvas"
          gl={{
            xr: { enabled: true },
            antialias: true,
            alpha: true,
            preserveDrawingBuffer: true
          }}
          camera={{
            position: [0, 1.6, 3],
            fov: 75,
            near: 0.1,
            far: 1000
          }}
          onCreated={({ gl }) => {
            // Add safety checks for WebXR methods that might not exist
            if (gl.xr) {
              if (!gl.xr.getEnvironmentBlendMode) {
                gl.xr.getEnvironmentBlendMode = () => 'opaque'
              }
              if (!gl.xr.getController) {
                gl.xr.getController = (index) => {
                  // Create a basic THREE.Group that can act as a controller
                  const controller = new THREE.Group()
                  controller.name = `XRController${index}`
                  if (DEBUG_AR_POLYFILLS) {
                    controller.addEventListener = (type, listener) =>
                      console.warn(`[XR Polyfill] controller.addEventListener('${type}') called, but this is a polyfill. The event will not fire because the browser does not support this XR feature.`)
                  } else {
                    controller.addEventListener = () => {}; // No-op
                  }
                  return controller
                }
              }
              if (!gl.xr.getControllerGrip) {
                gl.xr.getControllerGrip = (index) => {
                  const grip = new THREE.Group()
                  grip.name = `XRControllerGrip${index}`
                  if (DEBUG_AR_POLYFILLS) {
                    grip.addEventListener = (type, listener) =>
                      console.warn(`[XR Polyfill] grip.addEventListener('${type}') called, but this is a polyfill. The event will not fire because the browser does not support this XR feature.`)
                  } else {
                    grip.addEventListener = () => {}; // No-op
                  }
                  return grip
                }
              }
              if (!gl.xr.setReferenceSpaceType) {
                gl.xr.setReferenceSpaceType = (type) => DEBUG_AR_POLYFILLS && console.warn(`[XR Polyfill] setReferenceSpaceType('${type}') called, but this is a polyfill. The browser does not support this XR feature.`)
              }
              if (!gl.xr.setSession) {
                gl.xr.setSession = (session) => {
                  DEBUG_AR_POLYFILLS && console.warn('[XR Polyfill] gl.xr.setSession called, but this is a polyfill. Storing session reference manually.')
                  // Store the session for later use
                  gl.xr._session = session
                  return Promise.resolve()
                }
              }
              if (!gl.xr.getSession) {
                gl.xr.getSession = () => { // No-op
                  DEBUG_AR_POLYFILLS && console.warn('[XR Polyfill] gl.xr.getSession called, but this is a polyfill. Returning manually stored session.')
                  return gl.xr._session || null
                }
              }
              if (!gl.xr.addEventListener) {
                gl.xr.addEventListener = (type, listener) => DEBUG_AR_POLYFILLS && console.warn(`[XR Polyfill] gl.xr.addEventListener('${type}') called, but this is a polyfill. The event will not fire because the browser does not support this XR feature.`)
              }
            }

            // Add WebGL context loss handling
            gl.domElement.addEventListener('webglcontextlost', (event) => {
              console.warn('⚠️ WebGL context lost:', event)
              event.preventDefault()
            })

            gl.domElement.addEventListener('webglcontextrestored', (event) => {
              console.log('✅ WebGL context restored:', event)
            })

            console.log('🎨 AR Canvas created with WebXR support and safety polyfills')
          }}
        >
          <Suspense fallback={<LoadingSpinner/>}>
            <ExperienceARWrapper
              data={data}
              useNewARSystem={true}
              onARSessionEnd={handleARSessionEnd}
            />
          </Suspense>
        </Canvas>
      ) : (
        // Regular Mode Canvas - without XR (with camera controls)
        <CameraControlsErrorBoundary>
          <Canvas
            key="regular-canvas"
            camera={{
              position: [0, 1.6, 3],
              fov: 75,
              near: 0.1,
              far: 1000
            }}
          >
            <Suspense fallback={<LoadingSpinner/>}>
              <ExperienceOrbitControls data={data}/>
              {experienceState?.mode360 && <Experience360 data={data}/>}
              {experienceState?.modeModel && <ExperienceModel data={data}/>}
            </Suspense>
          </Canvas>
        </CameraControlsErrorBoundary>
      )}
    </>
  )
}