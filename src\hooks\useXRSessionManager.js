'use client'
import { useRef, useCallback } from 'react'

/**
 * Custom hook for managing XR sessions with proper cleanup
 * Provides centralized XR session management across components
 */
export function useXRSessionManager() {
  const activeSession = useRef(null)
  const hitTestSource = useRef(null)
  const animationLoopId = useRef(null)

  /**
   * Terminate the current XR session with proper cleanup
   */
  const terminateSession = useCallback(async () => {
    console.log('Terminating XR session...')
    
    try {
      // Clear hit test source
      if (hitTestSource.current) {
        hitTestSource.current = null
        console.log('Hit test source cleared')
      }

      // Clear animation loop
      if (animationLoopId.current) {
        // Get WebGL context from canvas
        const canvas = document.querySelector('canvas')
        if (canvas) {
          const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
          if (gl && gl.xr) {
            gl.xr.setAnimationLoop(null)
            gl.xr.enabled = false
            console.log('Animation loop cleared and XR disabled')
          }
        }
        animationLoopId.current = null
      }

      // End the XR session
      if (activeSession.current) {
        try {
          await activeSession.current.end()
          console.log('XR session ended successfully')
        } catch (error) {
          console.error('Error ending XR session:', error)
        }
        activeSession.current = null
      }

      // Additional cleanup - check for any remaining XR sessions
      if (navigator.xr) {
        const canvas = document.querySelector('canvas')
        if (canvas) {
          const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
          if (gl && gl.xr && gl.xr.getSession()) {
            try {
              await gl.xr.getSession().end()
              console.log('Additional XR session cleanup completed')
            } catch (error) {
              console.warn('Additional cleanup error (may be expected):', error)
            }
          }
        }
      }

      console.log('XR session termination completed')
    } catch (error) {
      console.error('Error during XR session termination:', error)
    }
  }, [])

  /**
   * Set the active session reference
   */
  const setActiveSession = useCallback((session) => {
    activeSession.current = session
  }, [])

  /**
   * Set the hit test source reference
   */
  const setHitTestSource = useCallback((source) => {
    hitTestSource.current = source
  }, [])

  /**
   * Set the animation loop ID reference
   */
  const setAnimationLoopId = useCallback((id) => {
    animationLoopId.current = id
  }, [])

  /**
   * Check if there's an active XR session
   */
  const hasActiveSession = useCallback(() => {
    return activeSession.current !== null
  }, [])

  /**
   * Get the current active session
   */
  const getActiveSession = useCallback(() => {
    return activeSession.current
  }, [])

  /**
   * Get the current hit test source
   */
  const getHitTestSource = useCallback(() => {
    return hitTestSource.current
  }, [])

  return {
    terminateSession,
    setActiveSession,
    setHitTestSource,
    setAnimationLoopId,
    hasActiveSession,
    getActiveSession,
    getHitTestSource
  }
}
