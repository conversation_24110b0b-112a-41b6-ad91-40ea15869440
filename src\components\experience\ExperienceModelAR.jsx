'use client'
import React, { useEffect, useCallback, useState, useRef } from 'react'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import * as THREE from 'three' // Import Three.js for Matrix4

export default function ExperienceModelAR({ data, refModelAR, modelPlacementMatrix }) {
  const { experienceState } = useExperienceContext()
  const { scene } = useThree()
  const refHideLevel = useRef(null)
  const [levelsToHideList, setLevelsToHideList] = useState([])
  const [isModelVisible, setIsModelVisible] = useState(false)

  const handleLevelToHde = () => {
    let priorityVisibleList = []
    let priorityInvisibleList = []
    if (experienceState?.levelToHide && levelsToHideList) {
      levelsToHideList?.map((i) => {
        if (i?.visible) {
          priorityVisibleList.push(i?.priority)
        } else {
          priorityInvisibleList.push(i?.priority)
        }
      })
      const minPriority = Math.min(...priorityInvisibleList)
      const maxPriority = Math.max(...priorityVisibleList)
      const targetObject = hideGroup?.getObjectByName(experienceState?.levelToHide?.name);
      levelsToHideList?.find(({ name, priority }) => {
        if (name === experienceState?.levelToHide?.name) {
          if (priority === minPriority) {
            targetObject.visible = true;
          } else if (priority === maxPriority) {
            targetObject.visible = false;
          }
        }
      })
    }
  }


  useEffect(() => {
    if (refModelAR.current) {
      refModelAR.current.matrixAutoUpdate = false;
    }
    const hideList = scene.getObjectByName('hideLevel')?.children
    if (hideList) {
      data?.hideLevel?.forEach((i) => {
        const child = hideList.find(({ name }) => i?.name === name)
        if (child) {
          child.priority = i?.priority || 0
        }
      })
      setLevelsToHideList(hideList)
    }
  }, [scene, data?.hideLevel, refModelAR])

  useEffect(() => {
    handleLevelToHde()
  }, [experienceState?.levelToHide])

  // Ensure model becomes visible when placement matrix is provided OR when no ref is used (new AR system)
  useEffect(() => {
    if (modelPlacementMatrix) {
      setIsModelVisible(true);
      console.log('ExperienceModelAR: Model placement matrix received, setting visibility to true');
    } else if (refModelAR === null) {
      // New AR system doesn't use refs, so make visible by default
      setIsModelVisible(true);
      console.log('ExperienceModelAR: No ref provided (new AR system), setting visibility to true');
    }
  }, [modelPlacementMatrix, refModelAR])

  useEffect(() => {
    if (refModelAR.current && modelPlacementMatrix) {
      const placementMatrix = new THREE.Matrix4().fromArray(modelPlacementMatrix);

      // The hit-test matrix provides position and orientation. We need to apply our own scale.
      const scaleValue = experienceState?.aRscale || 1.0;
      const scaleMatrix = new THREE.Matrix4().makeScale(scaleValue, scaleValue, scaleValue);

      // Combine the placement matrix with the scale matrix.
      // The order is important: M_final = M_placement * M_scale
      placementMatrix.multiply(scaleMatrix);

      // Apply the final combined matrix to the model's group
      refModelAR.current.matrix.copy(placementMatrix);

      // After placement, explicitly set the model to visible. This is the crucial fix.
      refModelAR.current.visible = true;

      // Update the state to trigger re-render with visibility
      setIsModelVisible(true);

      console.log('ExperienceModelAR: Model placed, scaled, and visibility set to TRUE.');

      // --- Optional Debugging: Decompose and log the final transform ---
      const position = new THREE.Vector3();
      refModelAR.current.matrix.decompose(position, new THREE.Quaternion(), new THREE.Vector3());
      console.log('ExperienceModelAR Debug: Final model position:', position.toArray());
    }
  }, [modelPlacementMatrix, refModelAR, experienceState?.aRscale]);

  return (
    <>
      <group
        ref={refModelAR}
        name="ExperienceModelAR"
        // Start as invisible. The useEffect for placement will make it visible.
        // This prevents a flash of the model at the origin before placement.
        visible={isModelVisible || !!modelPlacementMatrix || refModelAR === null}
      >
        {data?.modelsFiles?.map((model, index) =>
          <ExperienceGLTFLoader key={index} path={model} />
        )}
        <group ref={refHideLevel} name="hideLevel">
          {data?.hideLevel?.map((model, index) =>
            <ExperienceGLTFLoader key={index} path={model} />
          )}
        </group>
        {data?.supportFiles?.map((model, index) =>
          <ExperienceGLTFLoader key={index} path={model} />
        )}
        <group name="roomSnaps">
          {data?.roomSnaps?.map((model, index) =>
            <ExperienceGLTFLoader key={index} path={model} />
          )}
        </group>
      </group>
      <Environment preset="city" />
    </>
  )
}