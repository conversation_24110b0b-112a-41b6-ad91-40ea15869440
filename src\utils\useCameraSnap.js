import { useRef, useEffect, useCallback } from 'react';
import { useThree } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

export const useCameraSnap = (
  controlsRef, // Ref to OrbitControls
  experienceState,
  experienceDispatch,
  ACTIONS_EXPERIENCE
) => {
  const { camera, scene } = useThree();
  const initialCameraPosition = useRef(new THREE.Vector3());
  const initialCameraQuaternion = useRef(new THREE.Quaternion());
  const initialMinDistance = useRef(0);
  const initialMaxDistance = useRef(Infinity);

  useEffect(() => {
    // Store initial camera and OrbitControls settings on mount
    initialCameraPosition.current.copy(camera.position);
    initialCameraQuaternion.current.copy(camera.quaternion);
    if (controlsRef.current) {
      initialMinDistance.current = controlsRef.current.minDistance;
      initialMaxDistance.current = controlsRef.current.maxDistance;
    }
  }, [camera, controlsRef]);

  const snapCameraToObject = useCallback(
    (objectName) => {
      if (!controlsRef.current) return;

      const targetObject = scene.getObjectByName(objectName);

      if (targetObject) {
        // 1. Store current OrbitControls state
        const originalTarget = new THREE.Vector3();
        controlsRef.current.target.copy(originalTarget); // Store the current target

        // 2. Disable OrbitControls temporarily
        controlsRef.current.enabled = false;

        // 3. Calculate target position and orientation
        const targetPosition = new THREE.Vector3();
        targetObject.getWorldPosition(targetPosition);

        const targetQuaternion = new THREE.Quaternion();
        targetObject.getWorldQuaternion(targetQuaternion);

        // Calculate the direction the object is facing (assuming it's along its local Z-axis)
        const forward = new THREE.Vector3(0, 0, 1).applyQuaternion(targetQuaternion);
        const cameraLookAt = new THREE.Vector3().copy(targetPosition).add(forward.multiplyScalar(0.01)); // Look slightly in front of the object

        // 4. Smoothly move camera
        // For simplicity, we'll directly set for now. For smooth animation, use a tweening library.
        camera.position.copy(targetPosition);
        camera.lookAt(cameraLookAt); // Make camera face the direction of the object

        // 5. Adjust OrbitControls min/max distance for first-person view
        controlsRef.current.minDistance = 0.0;
        controlsRef.current.maxDistance = 0.5;
        controlsRef.current.target.copy(targetPosition); // Set OrbitControls target to the object's position

        // Dispatch action to update active room snap
        experienceDispatch({ type: ACTIONS_EXPERIENCE.SET_ACTIVE_ROOM_SNAP, payload: objectName });

        // 6. Re-enable OrbitControls after a delay
        setTimeout(() => {
          if (controlsRef.current) {
            controlsRef.current.enabled = true;
            controlsRef.current.update(); // Update controls after re-enabling
          }
        }, 2000); // 2 seconds delay
      } else {
        console.warn(`Object with name "${objectName}" not found in the scene.`);
      }
    },
    [camera, scene, controlsRef, experienceDispatch, ACTIONS_EXPERIENCE]
  );

  const resetCamera = useCallback(() => {
    if (controlsRef.current) {
      // Restore initial camera position and orientation
      camera.position.copy(initialCameraPosition.current);
      camera.quaternion.copy(initialCameraQuaternion.current);

      // Reset OrbitControls min/max distance and target
      controlsRef.current.minDistance = initialMinDistance.current;
      controlsRef.current.maxDistance = initialMaxDistance.current;
      controlsRef.current.target.copy(new THREE.Vector3(0,0,0)); // Assuming initial target is at origin or adjust as needed
      controlsRef.current.enabled = true;
      controlsRef.current.update();
    }
    experienceDispatch({ type: ACTIONS_EXPERIENCE.SET_ACTIVE_ROOM_SNAP, payload: null });
  }, [camera, controlsRef, experienceDispatch, ACTIONS_EXPERIENCE]);

  return { snapCameraToObject, resetCamera };
};