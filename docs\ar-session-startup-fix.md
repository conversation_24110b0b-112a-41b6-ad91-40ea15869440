# AR Session Startup Fix

## Issue Description
When AR mode was turned on, the AR session was not starting properly. The application would switch to AR mode but the WebXR session would not initialize.

## Root Cause Analysis
The problem was a **race condition** in the component lifecycle:

1. When `experienceState?.modeAR` became `true`, the `useEffect` in `ExperienceWorld.jsx` immediately called `handleEnterAR()`
2. At the same time, the `ExperienceAR` component was just being rendered for the first time
3. The `arRef.current` was still `null` because the `ExperienceAR` component hadn't mounted yet
4. This caused `handleEnterAR()` to fail silently since `arRef.current` was undefined

## Solution Implemented

### 1. Modified ExperienceWorld.jsx
- **Removed** the automatic AR session start from the `useEffect` that watches `experienceState?.modeAR`
- **Kept** the AR session end logic for when switching out of AR mode
- **Removed** the unused `handleEnterAR` function
- **Added** error handling props to the `ExperienceAR` component

### 2. Modified ExperienceAR.jsx
- **Added** automatic AR session startup in the component's `useEffect` hook
- **Added** cleanup logic to end AR session when component unmounts
- **Improved** error handling and logging

## Code Changes

### ExperienceWorld.jsx Changes
```javascript
// BEFORE: Race condition - tried to start AR before component mounted
useEffect(() => {
  experienceState?.modeAR ? handleEnterAR() : handleExitAR();
}, [experienceState?.modeAR]);

// AFTER: Only handle exit, let ExperienceAR handle its own startup
useEffect(() => {
  // Only handle exit when switching from AR to non-AR mode
  if (!experienceState?.modeAR) {
    handleExitAR();
  }
  // Note: AR session start is now handled by ExperienceAR component after it mounts
}, [experienceState?.modeAR]);
```

### ExperienceAR.jsx Changes
```javascript
// BEFORE: No automatic startup
useEffect(() => {
  // No automatic start/end here. Parent will call exposed functions.
}, []);

// AFTER: Automatic startup and cleanup
useEffect(() => {
  // Automatically start AR session when component mounts
  const initializeAR = async () => {
    try {
      console.log('🚀 ExperienceAR component mounted, starting AR session...');
      await startAR();
    } catch (error) {
      console.error('❌ Failed to auto-start AR session:', error);
      setErrorMessage('Failed to start AR session: ' + error.message);
      setShowError(true);
    }
  };

  initializeAR();

  // Cleanup: end AR session when component unmounts
  return () => {
    console.log('🧹 ExperienceAR component unmounting, ending AR session...');
    endAR();
  };
}, []);
```

## Benefits of This Fix

1. **Eliminates Race Condition**: AR session starts only after the component is fully mounted
2. **Better Error Handling**: Proper error reporting when AR session fails to start
3. **Automatic Cleanup**: AR session is properly ended when component unmounts
4. **Cleaner Architecture**: Each component manages its own lifecycle
5. **Improved Debugging**: Better console logging for troubleshooting

## Testing
- Switch to AR mode using the mode switcher
- AR session should now start automatically
- Check browser console for success/error messages
- Verify that AR features (reticle, hit testing, model placement) work properly

## Next Steps
- Test on actual AR-capable devices
- Verify that all AR features work as expected
- Consider adding loading states during AR session initialization
- Add user feedback for AR session status

## Git Commit Message
```
fix: resolve AR session startup race condition

- Move AR session initialization from parent useEffect to ExperienceAR component mount
- Add automatic cleanup when ExperienceAR component unmounts  
- Remove unused handleEnterAR function from ExperienceWorld
- Improve error handling and logging for AR session startup
- Fix race condition where AR session was attempted before component mounted

Fixes issue where AR mode would not start WebXR session properly.
```
