// middleware.js
// This middleware protects routes based on authentication status and user roles.

import NextAuth from "next-auth";
import { NextResponse } from "next/server";
// Import auth.config.js from src directory
import { authConfig } from "./src/auth.config";

const { auth } = NextAuth(authConfig);

export default auth((req) => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;

  // Define protected routes that require authentication
  const protectedRoutes = ["/dashboard", "/profile", "/projects"];
  const adminProtectedRoutes = ["/admin"];

  // Check if current path is a protected route
  const isProtectedRoute = protectedRoutes.some((route) =>
    nextUrl.pathname.startsWith(route)
  );

  const isAdminRoute = adminProtectedRoutes.some((route) =>
    nextUrl.pathname.startsWith(route)
  );

  // Redirect unauthenticated users trying to access protected routes
  if (!isLoggedIn && (isProtectedRoute || isAdminRoute)) {
    const signInUrl = new URL("/auth/signin", nextUrl);
    signInUrl.searchParams.set("callbackUrl", nextUrl.pathname);
    return NextResponse.redirect(signInUrl);
  }

  // Check admin permissions for admin routes
  if (isLoggedIn && isAdminRoute) {
    const userRole = req.auth?.user?.role;
    if (userRole !== "admin") {
      return NextResponse.redirect(new URL("/not-authorized", nextUrl));
    }
  }

  // Redirect authenticated users away from auth pages
  if (isLoggedIn && nextUrl.pathname.startsWith("/auth/signin")) {
    return NextResponse.redirect(new URL("/dashboard", nextUrl));
  }

  return NextResponse.next();
});

export const config = {
  // Only run middleware on specific routes to avoid conflicts
  matcher: [
    "/dashboard/:path*",
    "/admin/:path*",
    "/profile/:path*",
    "/projects/:path*",
    "/auth/signin"
  ],
};
