import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { CameraControls } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { MathUtils } from 'three'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'

export default function ExperienceCameraControls() {
  const {experienceState,experienceDispatch}=useExperienceContext()
  const {scene, camera}=useThree()
  const [snapObjects, setSnapObjects]=useState([])
  const [snapObject, setSnapObject]=useState({})
  const refControls=useRef(null)

  const handleSnapViewPoints = () => {
    if(!refControls.current || !snapObjects) return;
    if(snapObjects){
      const targetSnapPoint=snapObjects?.children?.find(({name})=>{
        return name===experienceState?.activeRoomSnap
      })
      if(targetSnapPoint){
        targetSnapPoint.traverse((child) => {
          if (child.isMesh) {
            // console.log(child)
            setSnapObject(child)
          }
        })
      }
    }
    if(snapObject && refControls.current){
      console.log(snapObject.position,refControls.current)
      refControls.current?.target?.copy(snapObject.position)
      console.log(refControls.current?.target)
      refControls.current?.update()
    }
  }
  
  useEffect(() => {
    handleSnapViewPoints()
  }, [experienceState?.activeRoomSnap,snapObjects])
  
  useEffect(() => {
    const roomSnapsObjects=scene.getObjectByName('roomSnaps')
    setSnapObjects(roomSnapsObjects)
  }, [])

  // console.log('ExperienceCameraControls:',snapObject)
  
  return (
    <CameraControls
      ref={refControls}
      smoothTime={5}
      minDistance={0}
      maxDistance={0.25}
      rotation={-0.015}
      maxPolarAngle={experienceState?.firstPersonView ? MathUtils.degToRad(85) : MathUtils.degToRad(120)}
      minPolarAngle={experienceState?.firstPersonView ? MathUtils.degToRad(10) : MathUtils.degToRad(45)}
    />
  )
}
