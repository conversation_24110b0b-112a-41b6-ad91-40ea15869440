'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense } from 'react'
import ExperienceAR from './ExperienceAR'
import { Environment } from '@react-three/drei'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModelDashboard'
import ExperienceControls from './ExperienceControlsDashboard'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'

export default function ExperienceWorld({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()
  // console.log('ExperienceWorld:',experienceState)

  // Note: AR mode disabled in dashboard to prevent Canvas HTML element errors
  // If AR is needed in dashboard, implement proper Canvas/UI separation like in main ExperienceWorld

  return (
    <CameraControlsErrorBoundary>
      <Canvas>
        <Suspense fallback={null}>
          {experienceState?.modeAR
            ? (
                <group>
                  {/* AR mode disabled in dashboard - would need proper Canvas/UI separation */}
                  <mesh position={[0, 0, -2]}>
                    <boxGeometry args={[1, 1, 1]} />
                    <meshBasicMaterial color="red" />
                  </mesh>
                </group>
              )
            : <>
                <ExperienceControls data={data} />
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
              </>
          }
        </Suspense>
      </Canvas>
    </CameraControlsErrorBoundary>
  )
}