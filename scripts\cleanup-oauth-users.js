const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

async function cleanupDatabase() {
  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  const db = client.db();
  
  console.log('🧹 Cleaning up OAuth user data...');
  
  // Find all <NAME_EMAIL>
  const users = await db.collection('users').find({ email: '<EMAIL>' }).toArray();
  console.log('Found users with this email:', users.length);
  
  if (users.length > 0) {
    // Keep the first user and delete the rest
    const userToKeep = users[0];
    const usersToDelete = users.slice(1);
    
    for (const user of usersToDelete) {
      await db.collection('users').deleteOne({ _id: user._id });
      console.log('Deleted duplicate user:', user._id.toString());
    }
    
    // Update the remaining user to have proper data
    await db.collection('users').updateOne(
      { _id: userToKeep._id },
      {
        $set: {
          name: userToKeep.name || 'victor chelemu',
          email: '<EMAIL>',
          emailVerified: null, // Will be set when OAuth links
          createdAt: userToKeep.createdAt || new Date(),
          updatedAt: new Date(),
          role: userToKeep.role || 'user'
        }
      }
    );
    console.log('Updated user:', userToKeep._id.toString());
  }
  
  console.log('✅ Database cleanup complete');
  await client.close();
}

cleanupDatabase().catch(console.error);
