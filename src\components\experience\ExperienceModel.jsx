'use client'
import React, {useEffect, useCallback, useState, useRef} from 'react'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

export default function ExperienceModel({data}) {
  const {experienceState}=useExperienceContext() // experienceDispatch is not used here
  const {scene}=useThree() 
  const refModel=useRef(null)
  const refHideLevel=useRef(null)
  const [levelsToHideList, setLevelsToHideList]=useState([])

  /**
   * Smart Level Visibility Management
   * Implements priority-based visibility control for hideLevel objects
   */
  const hideGroup = scene.getObjectByName('hideLevel');

  const handleLevelToHde = () => {
    let priorityVisibleList=[]
    let priorityInvisibleList=[]
    if(experienceState?.levelToHide && levelsToHideList){
      levelsToHideList?.map((i)=>{
        if(i?.visible){
          priorityVisibleList.push(i?.priority)
        }else{
          priorityInvisibleList.push(i?.priority)
        }
      })
      const minPriority = Math.min(...priorityInvisibleList)
      const maxPriority = Math.max(...priorityVisibleList)
      const targetObject = hideGroup?.getObjectByName(experienceState?.levelToHide?.name);
      levelsToHideList?.find(({name,priority})=>{
        if(name===experienceState?.levelToHide?.name){
          if(priority===minPriority){
            targetObject.visible = true;
          }else if(priority===maxPriority){
            targetObject.visible = false;
          }
        }
      })
    }
  }
  
  useEffect(() => {
    const hideList=scene.getObjectByName('hideLevel')?.children
    data?.hideLevel?.map((i)=>{
      hideList.find(({name})=>i?.name===name).priority=i?.priority || 0
      // console.log(hideList)
    })
    setLevelsToHideList(hideList)
  }, [])
  
  useEffect(() => {
    handleLevelToHde()
  }, [experienceState?.levelToHide])

  console.log('ExperienceModel Debug:', {
    position: refModel.current?.position,
    modelsFilesLength: data?.modelsFiles?.length,
    hideLevelLength: data?.hideLevel?.length,
    supportFilesLength: data?.supportFiles?.length,
    roomSnapsLength: data?.roomSnaps?.length,
    hasData: !!data
  })
  
  return (
    <>
      <group 
        name="ExperienceModel"
        ref={refModel}
        position={data?.position?.split(',').map(i=>Number(i))}
      >
        {data?.modelsFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
         <group ref={refHideLevel} name="hideLevel">
          {data?.hideLevel?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
        {data?.supportFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        <group name="roomSnaps">
          {data?.roomSnaps?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
      </group>
      <Environment preset="city"/>
    </>
  )
}
