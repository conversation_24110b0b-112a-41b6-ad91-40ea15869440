#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix duplicate MongoDB indexes on Building collection
 * This resolves the Mongoose warning about duplicate projectTitle indexes
 */

import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('❌ MONGODB_URI not found in environment variables');
  process.exit(1);
}

async function fixBuildingIndexes() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db();
    const buildingsCollection = db.collection('buildings');
    
    // Check existing indexes
    console.log('\n📋 Checking existing indexes...');
    const indexes = await buildingsCollection.indexes();
    console.log('Current indexes:', indexes.map(idx => ({ name: idx.name, key: idx.key })));
    
    // Drop the duplicate projectTitle index if it exists
    try {
      // First, try to drop any existing projectTitle indexes
      const projectTitleIndexes = indexes.filter(idx => 
        idx.key && idx.key.projectTitle !== undefined
      );
      
      for (const index of projectTitleIndexes) {
        if (index.name !== '_id_') { // Don't drop the _id index
          try {
            await buildingsCollection.dropIndex(index.name);
            console.log(`✅ Dropped index: ${index.name}`);
          } catch (error) {
            console.log(`ℹ️  Index ${index.name} does not exist or already dropped`);
          }
        }
      }
    } catch (error) {
      console.log('ℹ️  No duplicate indexes to drop');
    }
    
    // Create the correct unique index for projectTitle
    try {
      await buildingsCollection.createIndex(
        { projectTitle: 1 }, 
        { 
          unique: true,
          name: 'projectTitle_1_unique'
        }
      );
      console.log('✅ Created unique projectTitle index');
    } catch (error) {
      if (error.code === 11000) {
        console.log('⚠️  Duplicate projectTitle values found. Please clean up data first.');
      } else {
        console.log('ℹ️  ProjectTitle index already exists or error:', error.message);
      }
    }
    
    // Ensure other required indexes exist
    const requiredIndexes = [
      { key: { buildingType: 1 }, name: 'buildingType_1' },
      { key: { createdAt: -1 }, name: 'createdAt_-1' }
    ];
    
    for (const indexSpec of requiredIndexes) {
      try {
        await buildingsCollection.createIndex(indexSpec.key, { name: indexSpec.name });
        console.log(`✅ Ensured index exists: ${indexSpec.name}`);
      } catch (error) {
        console.log(`ℹ️  Index ${indexSpec.name} already exists`);
      }
    }
    
    // Final check - list all indexes
    console.log('\n📋 Final index list:');
    const finalIndexes = await buildingsCollection.indexes();
    finalIndexes.forEach(idx => {
      console.log(`  - ${idx.name}: ${JSON.stringify(idx.key)}`);
    });
    
    console.log('\n✅ Building indexes fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing building indexes:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
fixBuildingIndexes().catch(console.error);
