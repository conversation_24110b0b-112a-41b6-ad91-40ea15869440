import React, { useEffect, useRef, useState } from 'react' // eslint-disable-line no-unused-vars
import * as THREE from 'three'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

// New AR System Components
import ARSessionManagerNew from './ARSessionManagerNew'
import ARR<PERSON>le, { CrosshairReticle, TargetReticle } from './ARReticle'
import ARController, { ARControllerVisual, ARTouchHandler } from './ARController'
// import ARObjectManager, { useARObjectManager } from './ARObjectGenerator'
import ExperienceModelAR from './ExperienceModelAR'
import ARDOMOverlay from './ARDOMOverlay'
import * as THREE from 'three'

export default function ExperienceARWrapper({ data }) {
  const {experienceState}=useExperienceContext()

  // State for UI elements and messages
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)

  // New AR System State
  // This component will now always use the new AR system (based on @react-three/xr)
  const [arSessionData, setArSessionData] = useState(null)
  const [currentHitPose2, setCurrentHitPose2] = useState(null)
  const [placedModels, setPlacedModels] = useState([]) // Array of placed ExperienceModelAR instances
  const [reticleType, setReticleType] = useState('default') // 'default', 'crosshair', 'target'
  const [hitTestReady, setHitTestReady] = useState(false)
  const [sessionActive, setSessionActive] = useState(false)

  const resetModelPlacement = () => { // This function should be passed down to ExperienceAR
    console.log('ExperienceARWrapper: Resetting model placement (clearing placedModels)')
    setPlacedModels([])
    // ExperienceAR will handle hiding reticle/model visibility
  }

  // New AR System Handlers
  const handleARSessionStart = (session) => {
    console.log('🚀 New AR session started:', session)
    setSessionActive(true)
  }

  const handleARSessionEnd = () => {
    console.log('🛑 New AR session ended')
    setCurrentHitPose2(null)
    setPlacedModels([])
    setSessionActive(false)
    setHitTestReady(false)
  }

  const handleHitTestReady = (hitTestSource) => {
    console.log('🎯 Hit test source ready:', hitTestSource)
    console.log('🎯 Hit test source type:', typeof hitTestSource)
    setHitTestReady(true)
  }

  const handleHitPoseUpdate = (hitPose) => {
    console.log('🎯 Hit pose updated:', !!hitPose)
    setCurrentHitPose2(hitPose)
  }

  const handleControllerSelect = (event, controller) => {
    console.log('🎮 Controller select triggered in ExperienceWorld')
    console.log('🎮 Event:', event)
    console.log('🎮 Controller:', controller)
    console.log('🎮 currentHitPose2:', !!currentHitPose2)
    console.log('🎮 currentHitPose2 data:', currentHitPose2)

    if (currentHitPose2 && currentHitPose2.transform && currentHitPose2.transform.matrix) {
      console.log('✅ Using hit-test position for model placement')

      // Extract position and rotation from hit pose
      const matrix = new THREE.Matrix4().fromArray(currentHitPose2.transform.matrix)
      const position = new THREE.Vector3()
      const rotation = new THREE.Euler()
      const scale = new THREE.Vector3()

      matrix.decompose(position, new THREE.Quaternion(), scale)

      console.log('🎯 Hit-test position:', {
        x: position.x.toFixed(3),
        y: position.y.toFixed(3),
        z: position.z.toFixed(3)
      })

      // Create new model placement at exact hit-test location
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [position.x, position.y, position.z],
        rotation: [0, 0, 0], // Keep building upright
        scale: [0.3, 0.3, 0.3], // Smaller scale for better AR viewing
        hitPose: currentHitPose2,
        createdAt: Date.now(),
        placementType: 'hit-test'
      }

      // Add to placed models (limit to 5 models max)
      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 5) {
          updated.shift() // Remove oldest model
        }
        return updated
      })

      console.log('� Placed ExperienceModelAR at:', position)
      console.log('🏠 Total placed models:', placedModels.length + 1)
    } else {
      console.warn('⚠️ No hit pose available, placing at default position')

      // Fallback: place model at default position for testing
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [0, -0.5, -1], // Default position in front of camera, slightly below
        rotation: [0, 0, 0],
        scale: [0.3, 0.3, 0.3], // Smaller scale to match hit-test placement
        hitPose: null,
        createdAt: Date.now(),
        placementType: 'fallback'
      }

      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 5) {
          updated.shift()
        }
        return updated
      })

      console.log('🏠 Placed ExperienceModelAR at default position')
      console.log('🏠 Total placed models:', placedModels.length + 1)
    }
  }

  const handleModelRemoved = (modelId) => {
    setPlacedModels(prev => prev.filter(model => model.id !== modelId))
    console.log('➖ Model removed:', modelId)
  }

  const clearAllModels = () => {
    setPlacedModels([])
    console.log('🧹 All models cleared')
  }

  const cycleReticleType = () => {
    const types = ['default', 'crosshair', 'target']
    const currentIndex = types.indexOf(reticleType)
    const nextIndex = (currentIndex + 1) % types.length
    setReticleType(types[nextIndex])
    console.log('🎯 Reticle type changed to:', types[nextIndex])
  }

  return (
    // This component now directly renders the AR system using ARSessionManagerNew
    // and passes down the necessary state and handlers.
    <ARSessionManagerNew
      onSessionStart={handleARSessionStart}
      onSessionEnd={handleARSessionEnd}
      onHitTestReady={handleHitTestReady}
      onControllerSelect={handleControllerSelect}
    >
      {(sessionData) => (
        <>
          {/* Lighting for AR objects */}
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={0.8} />

              {/* AR Reticle */}
              <group>
                {/* Always visible test reticle */}
                <mesh position={[0, -0.5, -1]} rotation-x={-Math.PI / 2}>
                  <ringGeometry args={[0.1, 0.15, 32]} />
                  <meshBasicMaterial
                    color="#00ff00"
                    transparent={true}
                    opacity={0.9}
                    side={THREE.DoubleSide}
                    depthTest={false}
                    depthWrite={false}
                  />
                </mesh>

                {sessionData.hitTestReady ? (
                  <>
                    {reticleType === 'default' && (
                      <ARReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                      visible={true} // ARReticle component manages its own visibility based on hitPose
                      />
                    )}
                    {reticleType === 'crosshair' && (
                      <CrosshairReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                      visible={true}
                      />
                    )}
                    {reticleType === 'target' && (
                      <TargetReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                      visible={true}
                      />
                    )}
                  </>
                ) : (
                  // Fallback reticle when hit test is not ready
                  <mesh position={[0, 0, -1]}>
                    <ringGeometry args={[0.1, 0.15, 32]} />
                    <meshBasicMaterial
                      color="#ff0000"
                      transparent={true}
                      opacity={0.8}
                      side={THREE.DoubleSide}
                    />
                  </mesh>
                )}
              </group>

              {/* AR Controller */}
              <ARController
                onSelect={handleControllerSelect}
                enabled={sessionData.isSessionActive}
              />

              {/* AR Controller Visual */}
              <ARControllerVisual
                controllerIndex={0}
                showRay={true}
                rayColor="#ffffff"
                rayLength={5}
              />

              {/* Placed ExperienceModelAR instances */}
              {placedModels.map((model) => (
                <group
                  key={model.id}
                  position={model.position}
                  rotation={model.rotation}
                  scale={model.scale}
                >
                  <ExperienceModelAR
                    data={data}
                    modelPlacementMatrix={new THREE.Matrix4().compose(new THREE.Vector3(...model.position), new THREE.Quaternion().setFromEuler(new THREE.Euler(...model.rotation)), new THREE.Vector3(...model.scale)).elements}
                  />
                </group>
              ))}

              {/* Touch Handler for mobile */}
              <ARTouchHandler
                onTap={handleControllerSelect}
                enabled={sessionData.isSessionActive}
              />

              {/* Debug click handler */}
              <mesh
                position={[0, 0, -0.1]}
                onClick={handleControllerSelect}
                onPointerDown={handleControllerSelect}
              >
                <boxGeometry args={[0.1, 0.1, 0.1]} />
                <meshBasicMaterial
                  color="#ff00ff"
                  transparent={true}
                  opacity={0.5}
                />
              </mesh>
            </>
        )}
      )
  )
}
