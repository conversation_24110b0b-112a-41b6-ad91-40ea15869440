import React, { useEffect, useRef, useState } from 'react' // eslint-disable-line no-unused-vars
import ExperienceModelAR from './ExperienceModelAR'
import * as THREE from 'three'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'


// New AR System Components
import ARSessionManager from './ARSessionManager'
import ARR<PERSON>le, { CrosshairReticle, TargetReticle } from './ARReticle'
import ARController, { ARControllerVisual, ARTouchHandler } from './ARController'
// import ARObjectManager, { useARObjectManager } from './ARObjectGenerator'
import ExperienceModelAR from './ExperienceModelAR'
import ARDOMOverlay from './ARDOMOverlay'
import * as THREE from 'three'
import ExperienceARWrapper from './ExperienceARWrapper'

export default function ExperienceARWrapper({useNewARSystem,handleARSessionStart,handleARSessionEnd,handleHitTestReady,handleHitPoseUpdate,handleControllerSelect,placedModels,reticleType,currentHitPose,setErrorMessage,setShowError,showModel,setShowModel,data,refModelAR,refSessionAR,refController,refReticle,glSession,hitTestSource}) {
  const {experienceState}=useExperienceContext()
  // Legacy AR state management (for old ExperienceAR component)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)

   const previousModeAR = useRef(experienceState?.modeAR)

  // Legacy refs for AR functionality
  const refModelAR = useRef(null)
  const refSessionAR = useRef(null)
  const refController = useRef(null)
  const refReticle = useRef(null)
  const glSession = useRef(null)
  const hitTestSource = useRef(null)
  const currentHitPose = useRef(null)

  // New AR System State
  const [useNewARSystem, setUseNewARSystem] = useState(true)
  const [arSessionData, setArSessionData] = useState(null)
  const [currentHitPose2, setCurrentHitPose2] = useState(null)
  const [placedModels, setPlacedModels] = useState([]) // Array of placed ExperienceModelAR instances
  const [reticleType, setReticleType] = useState('default') // 'default', 'crosshair', 'target'
  const [hitTestReady, setHitTestReady] = useState(false)
  const [sessionActive, setSessionActive] = useState(false)

  const resetModelPlacement = () => {
    console.log('Resetting model placement')
    setShowModel(false)
    if (refModelAR.current) {
      refModelAR.current.visible = false
    }
    if (refReticle.current) {
      refReticle.current.visible = false
    }
  }

  // New AR System Handlers
  const handleARSessionStart = (session) => {
    console.log('🚀 New AR session started:', session)
    setSessionActive(true)
  }

  const handleARSessionEnd = () => {
    console.log('🛑 New AR session ended')
    setCurrentHitPose2(null)
    setPlacedModels([])
    setSessionActive(false)
    setHitTestReady(false)
  }

  const handleHitTestReady = (hitTestSource) => {
    console.log('🎯 Hit test source ready:', hitTestSource)
    console.log('🎯 Hit test source type:', typeof hitTestSource)
    setHitTestReady(true)
  }

  const handleHitPoseUpdate = (hitPose) => {
    console.log('🎯 Hit pose updated:', !!hitPose)
    setCurrentHitPose2(hitPose)
  }

  const handleControllerSelect = (event, controller) => {
    console.log('🎮 Controller select triggered in ExperienceWorld')
    console.log('🎮 Event:', event)
    console.log('🎮 Controller:', controller)
    console.log('🎮 currentHitPose2:', !!currentHitPose2)
    console.log('🎮 currentHitPose2 data:', currentHitPose2)

    if (currentHitPose2 && currentHitPose2.transform && currentHitPose2.transform.matrix) {
      console.log('✅ Using hit-test position for model placement')

      // Extract position and rotation from hit pose
      const matrix = new THREE.Matrix4().fromArray(currentHitPose2.transform.matrix)
      const position = new THREE.Vector3()
      const rotation = new THREE.Euler()
      const scale = new THREE.Vector3()

      matrix.decompose(position, new THREE.Quaternion(), scale)

      console.log('🎯 Hit-test position:', {
        x: position.x.toFixed(3),
        y: position.y.toFixed(3),
        z: position.z.toFixed(3)
      })

      // Create new model placement at exact hit-test location
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [position.x, position.y, position.z],
        rotation: [0, 0, 0], // Keep building upright
        scale: [0.3, 0.3, 0.3], // Smaller scale for better AR viewing
        hitPose: currentHitPose2,
        createdAt: Date.now(),
        placementType: 'hit-test'
      }

      // Add to placed models (limit to 5 models max)
      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 5) {
          updated.shift() // Remove oldest model
        }
        return updated
      })

      console.log('� Placed ExperienceModelAR at:', position)
      console.log('🏠 Total placed models:', placedModels.length + 1)
    } else {
      console.warn('⚠️ No hit pose available, placing at default position')

      // Fallback: place model at default position for testing
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [0, -0.5, -1], // Default position in front of camera, slightly below
        rotation: [0, 0, 0],
        scale: [0.3, 0.3, 0.3], // Smaller scale to match hit-test placement
        hitPose: null,
        createdAt: Date.now(),
        placementType: 'fallback'
      }

      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 5) {
          updated.shift()
        }
        return updated
      })

      console.log('🏠 Placed ExperienceModelAR at default position')
      console.log('🏠 Total placed models:', placedModels.length + 1)
    }
  }

  const handleModelRemoved = (modelId) => {
    setPlacedModels(prev => prev.filter(model => model.id !== modelId))
    console.log('➖ Model removed:', modelId)
  }

  const clearAllModels = () => {
    setPlacedModels([])
    console.log('🧹 All models cleared')
  }

  const cycleReticleType = () => {
    const types = ['default', 'crosshair', 'target']
    const currentIndex = types.indexOf(reticleType)
    const nextIndex = (currentIndex + 1) % types.length
    setReticleType(types[nextIndex])
    console.log('🎯 Reticle type changed to:', types[nextIndex])
  }

  // Handle XR session termination when switching out of AR mode
  useEffect(() => {
    const currentModeAR = experienceState?.modeAR

    // If we were in AR mode and now we're not, terminate the XR session
    if (previousModeAR.current && !currentModeAR) {
      console.log('Switching out of AR mode, terminating XR session...')

      // Simple fallback cleanup
      const terminateActiveSession = async () => {
        try {
          const canvas = document.querySelector('canvas')
          if (canvas) {
            const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
            if (gl && gl.xr && gl.xr.getSession()) {
              console.log('Found active XR session, ending it...')
              await gl.xr.getSession().end()
              gl.xr.enabled = false
              gl.xr.setAnimationLoop(null)
              console.log('XR session terminated successfully')
            }
          }
        } catch (error) {
          console.error('Error terminating XR session:', error)
        }
      }

      terminateActiveSession()
    }

    // Update the previous mode reference
    previousModeAR.current = currentModeAR
  }, [experienceState?.modeAR])
  return (
    (
      useNewARSystem ? (
        // New AR System
        <ARSessionManager
          onSessionStart={handleARSessionStart}
          onSessionEnd={handleARSessionEnd}
          onHitTestReady={handleHitTestReady}
        >
          {(sessionData) => (
            <>
              {/* Lighting for AR objects */}
              <ambientLight intensity={0.6} />
              <directionalLight position={[10, 10, 5]} intensity={0.8} />

              {/* AR Reticle */}
              <group>
                {/* Always visible test reticle */}
                <mesh position={[0, -0.5, -1]} rotation-x={-Math.PI / 2}>
                  <ringGeometry args={[0.1, 0.15, 32]} />
                  <meshBasicMaterial
                    color="#00ff00"
                    transparent={true}
                    opacity={0.9}
                    side={THREE.DoubleSide}
                    depthTest={false}
                    depthWrite={false}
                  />
                </mesh>

                {sessionData.hitTestReady ? (
                  <>
                    {reticleType === 'default' && (
                      <ARReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                        visible={true}
                      />
                    )}
                    {reticleType === 'crosshair' && (
                      <CrosshairReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                        visible={true}
                      />
                    )}
                    {reticleType === 'target' && (
                      <TargetReticle
                        hitTestSource={sessionData.hitTestSource}
                        onHitPoseUpdate={handleHitPoseUpdate}
                        visible={true}
                      />
                    )}
                  </>
                ) : (
                  // Fallback reticle when hit test is not ready
                  <mesh position={[0, 0, -1]}>
                    <ringGeometry args={[0.1, 0.15, 32]} />
                    <meshBasicMaterial
                      color="#ff0000"
                      transparent={true}
                      opacity={0.8}
                      side={THREE.DoubleSide}
                    />
                  </mesh>
                )}
              </group>



              {/* AR Controller */}
              <ARController
                onSelect={handleControllerSelect}
                enabled={sessionData.isSessionActive}
              />

              {/* AR Controller Visual */}
              <ARControllerVisual
                controllerIndex={0}
                showRay={true}
                rayColor="#ffffff"
                rayLength={5}
              />

              {/* Placed ExperienceModelAR instances */}
              {placedModels.map((model) => (
                <group
                  key={model.id}
                  position={model.position}
                  rotation={model.rotation}
                  scale={model.scale}
                >
                  <ExperienceModelAR
                    data={data}
                    refModelAR={null}
                  />
                </group>
              ))}

              {/* Touch Handler for mobile */}
              <ARTouchHandler
                onTap={handleControllerSelect}
                enabled={sessionData.isSessionActive}
              />

              {/* Debug click handler */}
              <mesh
                position={[0, 0, -0.1]}
                onClick={handleControllerSelect}
                onPointerDown={handleControllerSelect}
              >
                <boxGeometry args={[0.1, 0.1, 0.1]} />
                <meshBasicMaterial
                  color="#ff00ff"
                  transparent={true}
                  opacity={0.5}
                />
              </mesh>
            </>
          )}
        </ARSessionManager>
      ) : (
        // Legacy AR System
        <ExperienceAR
          data={data}
          refModelAR={refModelAR}
          refSessionAR={refSessionAR}
          refController={refController}
          refReticle={refReticle}
          glSession={glSession}
          hitTestSource={hitTestSource}
          currentHitPose={currentHitPose}
          setErrorMessage={setErrorMessage}
          setShowError={setShowError}
          showModel={showModel}
          setShowModel={setShowModel}
        />
      )
    )
  )
}
