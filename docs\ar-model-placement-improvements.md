# AR Model Placement and Reticle Visibility Improvements

## Issues Fixed

### 1. Model Placement at Hit-Test Source
- **Problem**: Model was not being properly positioned at the hit-test location
- **Solution**: Improved the `onSelect` function to properly apply the hit pose transform matrix

### 2. Reticle Visibility
- **Problem**: Reticle was not consistently visible to show users where the model would be placed
- **Solution**: Enhanced reticle visibility logic and added debug reticle for testing

### 3. AR Session Setup
- **Problem**: Hit-test source setup was not optimal for ground detection
- **Solution**: Improved reference space configuration and setup order

## Key Improvements Made

### 1. Enhanced Model Placement Logic

```javascript
const onSelect = () => {
  console.log('Controller select event triggered, placing Model in scene');
  if (refCurrentHitPose.current && refModelAR.current) {
    console.log('📍 Placing model at hit-test location');
    
    // Apply the hit pose transform matrix to the model
    refModelAR.current.matrix.fromArray(refCurrentHitPose.current.transform.matrix);
    refModelAR.current.matrixAutoUpdate = false;
    
    // Make the model visible
    refModelAR.current.visible = true;
    
    console.log('✅ Model placed and made visible');
    onModelPlaced(true); // Notify parent to show model
    setReticleVisible(false); // Hide reticle after placing
  } else {
    console.warn('⚠️ Cannot place model: missing hit pose or model ref');
  }
};
```

### 2. Improved AR Session Setup

```javascript
// Set up Three.js XR first
gl.xr.enabled = true
gl.xr.setReferenceSpaceType('local-floor')
await gl.xr.setSession(refSessionAR.current)

// Request an XR reference space for hit testing (use viewer for better hit detection)
const xrReferenceSpace = await session.requestReferenceSpace('viewer');
// Request a hit test source
refHitTestSource.current = await session.requestHitTestSource({ space: xrReferenceSpace });
```

### 3. Enhanced Reticle Visibility

```javascript
// Main reticle - visible when hit-test detects surface
{reticleVisible && !showModel && (
  <mesh
    ref={refReticle}
    matrixAutoUpdate={false}
    rotation-x={-Math.PI / 2}
  >
    <ringGeometry args={[0.15, 0.2, 32]} />
    <meshBasicMaterial
      color="#00ff00"
      transparent={true}
      opacity={0.9}
      side={2}
      depthTest={false}
      depthWrite={false}
    />
  </mesh>
)}

// Debug reticle - always visible for testing
{!showModel && (
  <mesh
    position={[0, -0.5, -1]}
    rotation-x={-Math.PI / 2}
  >
    <ringGeometry args={[0.05, 0.1, 16]} />
    <meshBasicMaterial
      color="#ff0000"
      transparent={true}
      opacity={0.5}
      side={2}
    />
  </mesh>
)}
```

### 4. Improved XR Frame Processing

```javascript
const onXRFrame = (_time, frame) => {
  if (!frame || !refHitTestSource.current) {
    refSessionAR.current?.requestAnimationFrame(onXRFrame);
    return;
  }

  // Do not process hit testing if the model is already placed
  if (showModel) {
    refSessionAR.current?.requestAnimationFrame(onXRFrame);
    return;
  }
  
  try {
    const hitTestResults = frame.getHitTestResults(refHitTestSource.current);
    
    if (hitTestResults.length > 0) {
      const hit = hitTestResults[0];
      const referenceLocalSpace = gl.xr.getReferenceSpace();
      if (referenceLocalSpace) {
        const hitPose = hit.getPose(referenceLocalSpace);
        if (hitPose) {
          // Store the current hit pose for model placement
          refCurrentHitPose.current = hitPose;

          // Update reticle position using matrix (if reticle exists)
          if (refReticle.current) {
            refReticle.current.matrix.fromArray(hitPose.transform.matrix);
            refReticle.current.matrixAutoUpdate = false;
          }
          
          // Show reticle when we have a valid hit
          if (!reticleVisible) {
            console.log('🎯 Hit detected, showing reticle');
            setReticleVisible(true);
          }
        }
      }
    } else {
      // Hide reticle when no hit test results
      if (reticleVisible) {
        console.log('❌ No hit detected, hiding reticle');
        setReticleVisible(false);
      }
      refCurrentHitPose.current = null;
    }
  } catch (error) {
    console.error('Error in XR frame processing:', error);
    if (reticleVisible) setReticleVisible(false);
  }
  
  refSessionAR.current?.requestAnimationFrame(onXRFrame);
};
```

### 5. Added Mobile Touch Support

```javascript
// Touch/Click handler for mobile devices
{!showModel && (
  <mesh
    position={[0, 0, -0.1]}
    onClick={onSelect}
    onPointerDown={onSelect}
    visible={false} // Invisible but clickable
  >
    <boxGeometry args={[10, 10, 0.1]} />
    <meshBasicMaterial transparent opacity={0} />
  </mesh>
)}
```

### 6. Proper Cleanup and Reset

```javascript
// Reset model visibility and transform
if (refModelAR.current) {
  refModelAR.current.visible = false;
  refModelAR.current.matrixAutoUpdate = true; // Re-enable auto update
  console.log('📦 Model hidden and reset');
}
```

## Benefits

1. **Accurate Model Placement**: Models now appear exactly where the reticle indicates
2. **Better User Feedback**: Users can clearly see where the model will be placed
3. **Improved Hit Detection**: Better reference space configuration for ground detection
4. **Mobile Support**: Touch/tap functionality for mobile AR devices
5. **Robust Error Handling**: Better logging and error recovery
6. **Proper Cleanup**: Models and reticles are properly reset when exiting AR

## Testing Instructions

1. Enter AR mode
2. Point device at a horizontal surface (floor/ground)
3. Look for the green reticle indicating where the model will be placed
4. Tap/click to place the model at the reticle location
5. Verify the model appears at the exact reticle position
6. Exit AR mode and verify proper cleanup

## Git Commit Message

```
feat: improve AR model placement and reticle visibility

- Fix model placement to use exact hit-test transform matrix
- Enhance reticle visibility with better styling and debug reticle
- Improve AR session setup with proper reference space configuration
- Add mobile touch/tap support for model placement
- Add proper model reset and cleanup when exiting AR
- Improve XR frame processing with better error handling
- Add comprehensive logging for debugging AR functionality

Models now appear exactly where the reticle indicates with clear visual feedback.
```
