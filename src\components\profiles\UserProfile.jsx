'use client';
// src/components/UserProfile/UserProfile.jsx
import React, { useState, useEffect } from 'react';

import ProfileDetails from './ProfileDetails';
import ProfileHeader from './ProfileHeader';
import UserActivity from './UserActivity';
import { useSession } from 'next-auth/react';
import { settings } from '@/libs/siteSettings';

const UserProfile = () => {
  const {data:session,status,update}=useSession()
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const simulateFetch = async () => {
      setLoading(true);
      setError(null);
      try {
        // await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
        setUser(session?.user)
        session && setLoading(false)
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    simulateFetch();
    // setUser(session?.user);
    // user && setLoading(false);
  }, [session]);

  const handleProfileSave = (updatedUserData) => {
    // This function would typically send updatedUserData to your backend
    // and then update the local state upon successful response.
    console.log("UserProfile received updated data, updating local state:", updatedUserData);
    setUser(updatedUserData); // Update local state with the new data
    // In a real app, you might show a success message here
  };

  if (loading) {
    return (
      <div className="flex absolute m-auto justify-center items-center h-1/4 rounded-lg shadow px-20 bg-gray-100">
        <div className="text-2xl text-gray-600">Loading user profile...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex absolute m-auto  justify-center items-center h-1/4 rounded-lg shadow px-20 bg-gray-100">
        <div className="text-2xl text-red-600">Error: {error.message}</div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex absolute m-auto justify-center items-center h-1/4 rounded-lg shadow px-20 bg-gray-100">
        <div className="text-2xl text-gray-600">User profile not found.</div>
      </div>
    );
  }

  console.log(status)

  return (
    <div className="max-w-4xl mx-auto p-5 sm:p-6 lg:p-8 bg-gray-100 rounded-lg shadow-xl my-8">
      {user && <ProfileHeader user={user} />}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-5"> {/* Use grid for layout, gap between sections */}
        <ProfileDetails user={user} onSave={handleProfileSave} />
        <UserActivity activities={settings.mockUserData.recentActivity} />
        {/* Add more sections here as needed */}
        {/* <ProfileSection title="Settings">
          <p className="text-gray-700">User settings options would go here.</p>
        </ProfileSection> */}
      </div>
    </div>
  );
};

export default UserProfile;