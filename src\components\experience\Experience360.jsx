'use client'
import React, { useRef, useState, useEffect } from 'react'
import { BackSide } from 'three'
import * as THREE from 'three'
import { useThree, useLoader } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { degToRad } from 'three/src/math/MathUtils'

export default function Experience360({data}) {
  const {experienceState}=useExperienceContext()
  const refSphere=useRef(null)
  const {scene}=useThree()

  // Safe texture URL extraction with fallback
  const currentTextureUrl = data?._360sImages?.[experienceState?.textureIndex || 0]?.url;

  // Only load texture if URL is valid
  const texture = currentTextureUrl ? useLoader(THREE.TextureLoader, currentTextureUrl) : null;
  
  useEffect(() => {
    if (texture && currentTextureUrl) {
      // console.log('Experience360: New texture displayed for URL:', currentTextureUrl);
    }
  }, [texture, currentTextureUrl])

  // Early return if no valid texture URL
  if (!currentTextureUrl) {
    // console.warn('Experience360: No valid texture URL provided');
    return null;
  }

  return (
    <>
      {texture && <mesh
        ref={refSphere}
        name="Experience360"
        scale={[1,1,-1]}
        rotation-y={degToRad(90)}
      >
        <meshBasicMaterial
          map={texture}
          side={BackSide}
        />
        <sphereGeometry args={[128,128,128]}/>
      </mesh>}
    </>
  )
}
