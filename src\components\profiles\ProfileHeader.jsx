'use client';

// src/components/ProfileHeader/ProfileHeader.jsx
import { useSession } from 'next-auth/react';
import React, { useState, useEffect } from 'react';
import { settings } from '@/libs/siteSettings';

const ProfileHeader = ({ user }) => {
  const [loading, setLoading] = useState(true);
  const { data: session, status } = useSession();
  const [userImage,setUserImage]=useState('')

  // Update user image when session changes
   useEffect(() => {
     if (user?.image) {
       // Handle both string and object image formats
       if (typeof user.image === 'string') {
         setUserImage(user.image);
       } else if (typeof user.image === 'object' && user.image.url) {
         setUserImage(user.image.url);
       }
     } else {
       setUserImage(null);
     }
   }, [user]);
  
  
  if (!user) {
    return <div className="text-center p-5 text-gray-600">Loading profile header...</div>;
  }

  // console.log('ProfileHeader:',user)

  return (
    <div className="flex flex-col items-center bg-white rounded-lg shadow-md p-8 mb-5 md:flex-row md:text-left md:items-start">
      <img
        src={userImage}
        alt={`${user.firstName} ${user.lastName}'s profile`}
        className="w-32 h-32 rounded-full object-cover mb-5 border-4 border-blue-500 md:mr-8 md:mb-0"
      />
      <div className="flex-grow">
        <h1 className="text-4xl font-bold text-gray-800 mb-2">{user?.name?.split(' ')[0]} {user?.name?.split(' ')[1]}</h1>
        <p className="text-lg text-gray-600 leading-relaxed mb-4">{settings.mockUserData.bio}</p>
      </div>
    </div>
  );
};

export default ProfileHeader;