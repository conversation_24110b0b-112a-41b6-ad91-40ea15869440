// src/app/api/auth/link-account/route.js
// API endpoint for linking OAuth accounts to existing user accounts

import { NextResponse } from "next/server";
import { auth } from "../../../../auth";
import dbConnect from "../../../../libs/mongoDb/connectToLuyariDB";
import clientPromise from "../../../../libs/mongoDb/mongodb-client-promise";

/**
 * POST /api/auth/link-account - Link OAuth account to existing user
 */
export async function POST(request) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    const { provider, providerAccountId, email } = await request.json();

    if (!provider || !providerAccountId || !email) {
      return NextResponse.json(
        { message: "Missing required fields: provider, providerAccountId, email" },
        { status: 400 }
      );
    }

    // Verify the email matches the current user's email
    if (session.user.email !== email) {
      return NextResponse.json(
        { message: "Email mismatch. Cannot link account." },
        { status: 403 }
      );
    }

    await dbConnect();
    const client = await clientPromise;
    const db = client.db();
    
    // Check if this OAuth account is already linked to another user
    const existingAccount = await db.collection("accounts").findOne({
      provider: provider,
      providerAccountId: providerAccountId
    });

    if (existingAccount) {
      return NextResponse.json(
        { message: "This OAuth account is already linked to another user" },
        { status: 409 }
      );
    }

    // Create the account link
    const accountData = {
      userId: session.user.id,
      type: "oauth",
      provider: provider,
      providerAccountId: providerAccountId,
      scope: "email profile",
      token_type: "bearer",
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection("accounts").insertOne(accountData);

    if (result.acknowledged) {
      // Update user record to mark email as verified if linking Google
      if (provider === "google") {
        await db.collection("users").updateOne(
          { _id: session.user.id },
          { 
            $set: { 
              emailVerified: new Date(),
              updatedAt: new Date()
            }
          }
        );
      }

      return NextResponse.json({
        message: "Account linked successfully",
        provider: provider
      });
    } else {
      throw new Error("Failed to link account");
    }

  } catch (error) {
    console.error("Account linking error:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET /api/auth/link-account - Get linked accounts for current user
 */
export async function GET(request) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    await dbConnect();
    const client = await clientPromise;
    const db = client.db();
    
    const accounts = await db.collection("accounts").find({
      userId: session.user.id
    }).toArray();

    const linkedAccounts = accounts.map(account => ({
      provider: account.provider,
      type: account.type,
      createdAt: account.createdAt
    }));

    return NextResponse.json({
      linkedAccounts: linkedAccounts
    });

  } catch (error) {
    console.error("Error fetching linked accounts:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/auth/link-account - Unlink OAuth account
 */
export async function DELETE(request) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    const { provider } = await request.json();

    if (!provider) {
      return NextResponse.json(
        { message: "Provider is required" },
        { status: 400 }
      );
    }

    await dbConnect();
    const client = await clientPromise;
    const db = client.db();
    
    // Check if user has a password or other accounts before unlinking
    const user = await db.collection("users").findOne({ _id: session.user.id });
    const userAccounts = await db.collection("accounts").find({
      userId: session.user.id
    }).toArray();

    // Prevent unlinking if it's the only authentication method and no password
    if (userAccounts.length === 1 && !user.password) {
      return NextResponse.json(
        { message: "Cannot unlink the only authentication method. Please set a password first." },
        { status: 400 }
      );
    }

    const result = await db.collection("accounts").deleteOne({
      userId: session.user.id,
      provider: provider
    });

    if (result.deletedCount > 0) {
      return NextResponse.json({
        message: "Account unlinked successfully",
        provider: provider
      });
    } else {
      return NextResponse.json(
        { message: "Account not found or already unlinked" },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error("Account unlinking error:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}
