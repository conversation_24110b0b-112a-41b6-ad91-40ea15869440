'use client'
import React, { useState, useRef, useEffect } from 'react';
import { OrbitControls } from '@react-three/drei'
import { degToRad } from 'three/src/math/MathUtils'
import { useThree } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';

export default function ExperienceControls({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()
  const [target, setTarget]=useState(null);
  const [snapObject, setSnapObject]=useState({});
  const [enableControls, setEnableControls]=useState(true);
  const refControls=useRef(null)
  const {camera,scene}=useThree()

  // Safely parse data properties with fallbacks
  const safeData = {
    minDistance: parseFloat(data?.minDistance) || 1,
    maxDistance: parseFloat(data?.maxDistance) || 10,
    ...data
  };

  const snap = (targetPosition, targetLookAt) => {
    try {
      if (refControls.current && camera && targetPosition && targetLookAt) {
        // Set the camera's new position
        camera.position.copy(targetPosition);

        // Make the camera look at the targetLookAt point
        camera.lookAt(targetLookAt);

        // Update OrbitControls' target to match the camera's new lookAt point
        // This is crucial for OrbitControls to behave correctly after the snap
        refControls.current.target.copy(targetLookAt);

        // Update controls to reflect the changes
        refControls.current.update();
      }
    } catch (error) {
      console.error('Error in camera snap:', error);
    }
  };

  useEffect(() => {
    try {
      if (experienceState?.activeRoomSnap && scene) {
        const targetObject = scene.getObjectByName(experienceState?.activeRoomSnap);
        if (targetObject) {
          targetObject.traverse((child) => {
            if (child.isMesh) {
              child.name = experienceState?.activeRoomSnap;
              setSnapObject(child)
            }
          });
          if (snapObject && camera) {
            snap(snapObject?.position, snapObject?.position)
          }
        }
      }
    } catch (error) {
      console.error('Error in room snap effect:', error);
    }
  }, [scene, experienceState?.activeRoomSnap, snapObject, camera]);
  
  // console.log('ExperienceControls:',experienceState)
  return (
    <OrbitControls
      ref={refControls}
      minDistance={experienceState?.firstPersonView ? 0 : safeData.minDistance}
      maxDistance={experienceState?.firstPersonView ? 0.5 : safeData.maxDistance}
      maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
      minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
      rotateSpeed={-0.25}
      enabled={enableControls}
      enablePan={false}
    />
  )
}
