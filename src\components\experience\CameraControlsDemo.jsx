'use client';
import React, { useRef } from 'react';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';

/**
 * Demo component to showcase the improved camera controls
 * This component provides UI buttons to test camera snap functionality
 */
export default function CameraControlsDemo({ data }) {
  const { experienceState, experienceDispatch } = useExperienceContext();
  const controlsRef = useRef(null);

  const handleSnapToRoom = (roomSnapName) => {
    experienceDispatch({ 
      type: 'SET_ACTIVE_ROOM_SNAP', 
      payload: roomSnapName 
    });
  };

  const handleCycleSnaps = () => {
    if (controlsRef.current?.cycleSnapPoints) {
      controlsRef.current.cycleSnapPoints();
    }
  };

  const handleResetCamera = () => {
    if (controlsRef.current?.resetCamera) {
      controlsRef.current.resetCamera();
    }
  };

  const toggleFirstPersonView = () => {
    experienceDispatch({ 
      type: 'SET_FIRST_PERSON_VIEW', 
      payload: !experienceState?.firstPersonView 
    });
  };

  if (!data?.roomSnaps || data.roomSnaps.length === 0) {
    return null;
  }

  return (
    <div className="absolute top-4 left-4 bg-black/75 text-white p-4 rounded-lg z-10">
      <h3 className="text-lg font-bold mb-3">Camera Controls Demo</h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={toggleFirstPersonView}
          className="w-full px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm"
        >
          {experienceState?.firstPersonView ? 'Third Person' : 'First Person'} View
        </button>
        
        <button
          onClick={handleCycleSnaps}
          className="w-full px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm"
        >
          Cycle Snap Points
        </button>
        
        <button
          onClick={handleResetCamera}
          className="w-full px-3 py-2 bg-red-600 hover:bg-red-700 rounded text-sm"
        >
          Reset Camera
        </button>
      </div>

      <div className="space-y-1">
        <h4 className="text-sm font-semibold">Room Snaps:</h4>
        {data.roomSnaps.map((snap, index) => (
          <button
            key={snap.id || index}
            onClick={() => handleSnapToRoom(snap.name)}
            className={`w-full px-2 py-1 text-xs rounded ${
              experienceState?.activeRoomSnap === snap.name
                ? 'bg-yellow-600 hover:bg-yellow-700'
                : 'bg-gray-600 hover:bg-gray-700'
            }`}
          >
            {snap.name}
          </button>
        ))}
      </div>

      <div className="mt-3 text-xs text-gray-300">
        <p>Active: {experienceState?.activeRoomSnap || 'None'}</p>
        <p>Mode: {experienceState?.firstPersonView ? 'FPV' : '3rd Person'}</p>
      </div>
    </div>
  );
}
