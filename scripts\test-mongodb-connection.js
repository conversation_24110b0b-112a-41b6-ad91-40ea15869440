// Test MongoDB connection
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const uri = process.env.MONGODB_URI;

if (!uri) {
  console.error('❌ MONGODB_URI not found in environment variables');
  process.exit(1);
}

console.log('🔗 Testing MongoDB connection...');
console.log('📍 URI:', uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')); // Hide credentials

const client = new MongoClient(uri, {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
});

async function testConnection() {
  try {
    console.log('⏳ Connecting to MongoDB...');
    await client.connect();
    
    console.log('✅ Connected to MongoDB successfully!');
    
    // Test database access
    const db = client.db('luyari');
    const collections = await db.listCollections().toArray();
    
    console.log('📋 Available collections:');
    collections.forEach(col => {
      console.log(`  - ${col.name}`);
    });
    
    // Test a simple query
    const usersCount = await db.collection('users').countDocuments();
    console.log(`👥 Users collection has ${usersCount} documents`);
    
    console.log('🎉 MongoDB connection test successful!');
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:');
    console.error('Error:', error.message);
    console.error('Code:', error.code);
    
    if (error.code === 'ENOTFOUND') {
      console.error('💡 This usually means DNS resolution failed. Check your internet connection.');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 Connection refused. Check if MongoDB is running and accessible.');
    } else if (error.message.includes('authentication')) {
      console.error('💡 Authentication failed. Check your username and password.');
    }
    
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

testConnection();
