// scripts/fix-admin-password.js
// <PERSON><PERSON><PERSON> to add password to admin user

import { MongoClient, ObjectId } from 'mongodb';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const uri = process.env.MONGODB_URI;
const adminEmail = '<EMAIL>';
const adminPassword = 'AdminPassword123!';

async function fixAdminPassword() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    // Find admin user
    const adminUser = await usersCollection.findOne({ email: adminEmail });
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log('📋 Current admin user status:');
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Has Password: ${adminUser.password ? 'Yes' : 'No'}`);
    
    // Hash the password
    console.log('🔐 Hashing password...');
    const hashedPassword = await bcrypt.hash(adminPassword, 10);
    
    // Update the user with password
    const result = await usersCollection.updateOne(
      { _id: adminUser._id },
      { 
        $set: { 
          password: hashedPassword,
          name: 'Victor Chelemu', // Fix the name
          emailVerified: new Date(),
          createdAt: adminUser.createdAt || new Date(),
          updatedAt: new Date()
        }
      }
    );
    
    if (result.modifiedCount > 0) {
      console.log('✅ Admin user password updated successfully!');
      console.log(`🔑 Email: ${adminEmail}`);
      console.log(`🔑 Password: ${adminPassword}`);
      console.log('⚠️  Please change this password after first login');
      
      // Verify the update
      const updatedUser = await usersCollection.findOne({ email: adminEmail });
      console.log('\n📋 Updated admin user:');
      console.log(`   Name: ${updatedUser.name}`);
      console.log(`   Email: ${updatedUser.email}`);
      console.log(`   Role: ${updatedUser.role}`);
      console.log(`   Has Password: ${updatedUser.password ? 'Yes' : 'No'}`);
      console.log(`   Email Verified: ${updatedUser.emailVerified ? 'Yes' : 'No'}`);
      
    } else {
      console.log('❌ Failed to update admin user password');
    }
    
  } catch (error) {
    console.error('❌ Error fixing admin password:', error);
  } finally {
    await client.close();
  }
}

fixAdminPassword();
