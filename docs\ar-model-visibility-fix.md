# AR Model Visibility Fix

## Issue
The refModelAR was not becoming visible after placement in AR mode. The ExperienceModelAR component was starting with `visible={false}` and relying solely on a useEffect to set the ref's visibility, which could have timing issues.

## Solution
Modified the ExperienceModelAR component to use state-based visibility management:

### Changes Made

1. **Added State Management**
   - Added `isModelVisible` state to track visibility
   - Component now uses both state and props to determine visibility

2. **Enhanced Visibility Logic**
   - Updated the group's `visible` prop to: `visible={isModelVisible || !!modelPlacementMatrix || refModelAR === null}`
   - This ensures visibility in three scenarios:
     - When state indicates model should be visible
     - When a placement matrix is provided (legacy AR system)
     - When no ref is provided (new AR system using position/rotation/scale props)

3. **Improved useEffect Handling**
   - Added separate useEffect to set visibility when modelPlacementMatrix is received
   - Enhanced existing placement useEffect to update both ref visibility and state

### Files Modified
- `src/components/experience/ExperienceModelAR.jsx`

### How It Works
- **Legacy AR System**: Uses modelPlacementMatrix prop and ref-based positioning
- **New AR System**: Uses position/rotation/scale props on wrapper group, model is visible by default
- **Both Systems**: Now properly handle visibility state to ensure models appear after placement

### Testing
Test both AR systems:
1. Legacy AR system (ExperienceAR component)
2. New AR system (ExperienceARWrapper component)

Verify that models become visible immediately after placement in both systems.

## Additional Fixes

### Created New ARSessionManager for Render Props
- **Issue**: New AR system was trying to use legacy ARSessionManager with render prop pattern
- **Solution**: Created `ARSessionManagerNew.jsx` that supports children as render props
- **Files**: `src/components/experience/ARSessionManagerNew.jsx`

### Fixed Controller Integration
- Added controller support to new ARSessionManager
- Integrated controller select events for model placement
- Added proper controller cleanup on session end

### Enhanced Model Visibility Logic
- Updated ExperienceModelAR to handle both AR systems
- Models in new AR system (using position/rotation/scale props) are now visible by default
- Legacy AR system continues to use placement matrix for visibility

### Fixed Mode Switching Issues
- **Issue**: ExperienceWorld was using legacy ExperienceAR instead of ExperienceARWrapper
- **Issue**: MODE_AR_OFF action didn't set a default mode, leaving users in limbo
- **Issue**: textureIndex was set to `false` instead of `0`, causing Experience360 to return null
- **Solution**: Updated ExperienceWorld to use ExperienceARWrapper with new AR system
- **Solution**: Fixed MODE_AR_OFF to default back to 360 mode with proper state
- **Solution**: Set textureIndex initial value to 0 instead of false

### Added Debug Logging
- Added comprehensive debug logging to Experience360, ExperienceModel, and ExperienceWorld
- Helps troubleshoot data loading and mode switching issues
- Console logs show texture URLs, model counts, and mode states

### Fixed Import Errors
- **Issue**: Circular import where ExperienceARWrapper was importing itself
- **Issue**: Duplicate imports of THREE and ExperienceModelAR
- **Solution**: Cleaned up all duplicate and circular imports
- **Result**: Server now starts without compilation errors

### Fixed AR Canvas Configuration
- **Issue**: AR mode was showing 360 world with wrong camera settings
- **Issue**: Canvas wasn't properly configured for WebXR sessions
- **Solution**: Created separate Canvas configurations for AR vs regular modes
- **Solution**: AR Canvas has `gl.xr.enabled: true` and proper camera settings
- **Solution**: Added Canvas key props to force re-render when switching modes
- **Result**: AR mode now properly initializes WebXR sessions

### Fixed WebXR Compatibility Errors
- **Issue**: `gl.xr.getController is not a function` TypeError
- **Issue**: `renderer.xr.getEnvironmentBlendMode is not a function` TypeError
- **Issue**: `gl.xr.setReferenceSpaceType is not a function` TypeError
- **Issue**: `gl.xr.setSession is not a function` TypeError
- **Issue**: Three.js WebXR method compatibility issues
- **Issue**: WebGL context lost errors during AR session transitions
- **Solution**: Added safety checks for all WebXR methods availability
- **Solution**: Added comprehensive WebXR method polyfills in Canvas onCreated callback
- **Solution**: Added WebGL context loss/restore event handling
- **Solution**: Enhanced error handling and logging for AR session lifecycle
- **Result**: Eliminated WebXR compatibility errors and improved debugging

### Fixed AR Session Request Issues
- **Issue**: "The optional feature 'hand-tracking' is not supported" causing session failure
- **Issue**: AR session starting but immediately ending (360° scene taking over)
- **Issue**: DOM overlay configuration causing session rejection
- **Solution**: Removed unsupported 'hand-tracking' feature from session request
- **Solution**: Added intelligent DOM overlay container detection
- **Solution**: Added session duration tracking and startup failure detection
- **Solution**: Simplified session configuration with minimal required features
- **Result**: AR sessions now start successfully without immediate termination

## Git Commit Message
```
fix: resolve AR session startup failures and WebXR compatibility issues

- Create ARSessionManagerNew with render prop support for new AR system
- Add controller integration for model placement in new AR system
- Fix ExperienceModelAR visibility for both legacy and new AR systems
- Update ExperienceWorld to use ExperienceARWrapper instead of legacy ExperienceAR
- Fix MODE_AR_OFF action to default back to 360 mode properly
- Fix textureIndex initial value from false to 0 for Experience360 component
- Clean up circular and duplicate imports in ExperienceARWrapper
- Create separate Canvas configurations for AR vs regular modes with proper WebXR setup
- Remove unsupported 'hand-tracking' feature causing AR session failures
- Add intelligent DOM overlay container detection for session requests
- Add WebXR compatibility polyfills for getController, getEnvironmentBlendMode, and setReferenceSpaceType
- Add safety checks for WebXR method availability across all AR components
- Add session duration tracking and startup failure detection
- Enhanced AR session lifecycle logging and error handling
- Add debug logging to troubleshoot data loading and mode switching
- Ensure reticle appears and models are visible after placement
- Add proper session and controller cleanup
```
