// scripts/setup-admin-user.js
// Script <NAME_EMAIL> as an admin user

import { MongoClient } from 'mongodb';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const uri = process.env.MONGODB_URI;
const adminEmail = '<EMAIL>';

async function setupAdminUser() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    // Check if admin user already exists
    const existingUser = await usersCollection.findOne({ email: adminEmail });
    
    if (existingUser) {
      // Update existing user to admin role
      const result = await usersCollection.updateOne(
        { email: adminEmail },
        { 
          $set: { 
            role: 'admin',
            updatedAt: new Date()
          }
        }
      );
      
      if (result.modifiedCount > 0) {
        console.log(`✅ Updated ${adminEmail} to admin role`);
      } else {
        console.log(`ℹ️  ${adminEmail} already has admin role`);
      }
    } else {
      // Create new admin user
      const hashedPassword = await bcrypt.hash('AdminPassword123!', 10);
      
      const adminUser = {
        name: 'Victor Chelemu',
        email: adminEmail,
        password: hashedPassword,
        role: 'admin',
        invites: [],
        invoices: [],
        recipets: [],
        messgages: [],
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await usersCollection.insertOne(adminUser);
      
      if (result.acknowledged) {
        console.log(`✅ Created admin user: ${adminEmail}`);
        console.log(`🔑 Default password: AdminPassword123!`);
        console.log(`⚠️  Please change the password after first login`);
      } else {
        console.error('❌ Failed to create admin user');
      }
    }
    
    // Verify admin user
    const adminUser = await usersCollection.findOne({ email: adminEmail });
    console.log('\n📋 Admin User Details:');
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Name: ${adminUser.name}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   Created: ${adminUser.createdAt}`);
    console.log(`   Email Verified: ${adminUser.emailVerified ? 'Yes' : 'No'}`);
    
    console.log('\n🎉 Admin user setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error setting up admin user:', error);
  } finally {
    await client.close();
  }
}

setupAdminUser();
