use the building model in the models folder in the mongoDb folder to build a build endpoint with all the relevant crud functionality. build an input form based on the fields needed in the the building model.break the varous input type into components for ease of editing instead of having a long code file.

buildingType input should be a choice of three, multi-storey, single-storey, multi-residence.

the following are the fields in the building model should be text inputs the feild are for projectTitle,price,buildingTitle,buildingType,desc,position,arPosition,minDistance,maxDistance,buildingRole,features,outroSection,warrantyInformation,shippingInformation,returnPolicy

the following are array fields and should have an input that allows for multiple entries but their should be no repetition of tags, color, collections

the following are file type entries and we should have an option to upload serveral flies,renders,drawings,modelsFiles,hideLevel,supportFiles,_360sImages,roomSnaps,presentationDrawings,constructionDrawingsPdf,constructionDrawingsDwg.
a firebase storage integration should be done for the file upload. the file upload component should be able to handle all the file types and upload them to firebase storage. the file upload component should return the file url and the file name. the file url should be stored in the mongoDB and the file name should be used to reference the file in firebase storage.
the files uploaded should be in the luyari/the respective projectTitle title.
renders, drawings,_360sImages are images types.
modelsFiles,hideLevel,supportFiles,roomSnaps are glb types.
presentationDrawings,constructionDrawingsPdf, are pdf types.
constructionDrawingsDwg are dwgs types.
files can be uploaded until a project title is available. the hidelevel files will need a priority input that can bew set before the being submittied to the mongoDB.

the buildingHighlights array input should have a title and a description input that will create an object list that should ensure there's always a unique entry object and its contents wise to avoid repition.

builingSummary object should have the an input of the following, length, width, baths, levels, cars, beds. these will be the object entries for the building.