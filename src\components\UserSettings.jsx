'use client';

import React, { useState, useEffect } from 'react';
import { FaUser } from 'react-icons/fa6';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import AuthPopup from './AuthPopup';

export default function UserSettings() {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated' && session?.user;
  const [userImage, setUserImage] = useState(null);

  // Update user image when session changes
  useEffect(() => {
    if (isAuthenticated && session?.user?.image) {
      // Handle both string and object image formats
      if (typeof session.user.image === 'string') {
        setUserImage(session.user.image);
      } else if (typeof session.user.image === 'object' && session.user.image.url) {
        setUserImage(session.user.image.url);
      }
    } else {
      setUserImage(null);
    }
  }, [session, isAuthenticated, status]);

  const togglePopup = () => {
    setIsPopupOpen(!isPopupOpen);
  };

  // console.log(userImage,session)

  return (
    <div className='flex w-fit h-full items-center justify-center'>
      {isAuthenticated 
        ? (
            <>
              {/* Show user image if available */}
              <div
                className='relative w-10 h-10 rounded-full overflow-hidden cursor-pointer border border-current'
                onClick={togglePopup}
              >
                {userImage 
                  ? (
                      <Image
                        src={userImage}
                        alt={session.user.name || 'User'}
                        fill
                        className="object-cover"
                      />
                    ) 
                  : (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center text-gray-500 text-sm font-light">
                        {(session.user.name?.charAt(0) || session.user.email?.charAt(0) || '?').toUpperCase()}
                      </div>
                    )
                  }
              </div>

              {/* Display user name if logged in */}
              {session.user.name && (
                <span className='ml-2 text-sm font-light max-w-20 text-wrap hidden md:block'>
                  hello, {session.user.name}
                </span>
              )}
            </>
          ) 
        : (
            // Show sign in button when not authenticated
            <div className="flex items-center">
              <div className="flex items-center cursor-pointer" onClick={togglePopup}>
                {/* <FaUser className='flex items-center justify-center rounded-full p-1 text-3xl'/> */}
                <span className='text-sm font-light hidden md:block'>
                  Sign In
                </span>
              </div>
            </div>
          )}

      {/* Auth Popup - always show but with different content based on auth status */}
      <AuthPopup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
      />
    </div>
  );
}
