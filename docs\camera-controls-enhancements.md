# Three.js Camera Controls Enhancements

## Overview
Implemented two major enhancements to the Three.js camera controls system:
1. **Smart Level Visibility Management** in ExperienceModel component
2. **First-Person Camera Snap System** in ExperienceControls component

## Part 1: ExperienceModel - Smart Level Visibility Management

### Enhancement Details
Enhanced the `handleLevelToHde` function with priority-based visibility control logic.

### Key Features

#### 1. Scope Control
- Only operates on objects within the group named 'hideLevel'
- Validates existence of hideLevel group before processing

#### 2. Priority-Based Visibility Logic
```javascript
// Priority determination: array index = priority (lower index = higher priority)
const targetPriority = targetLevelIndex; // Array index from data.hideLevel

// When hiding objects: Find highest priority among visible objects
const highestPriorityVisible = Math.min(...visibleLevels.map(l => l.priority));

// When showing objects: Find lowest priority among hidden objects  
const lowestPriorityHidden = Math.max(...hiddenLevels.map(l => l.priority));
```

#### 3. Smart Visibility Rules
- **Hiding**: Only hide if target has the **highest priority** among currently **visible** objects
- **Showing**: Only show if target has the **lowest priority** among currently **hidden** objects

#### 4. State Analysis
- Analyzes current visibility state of all hideLevel objects
- Matches object names to `data.hideLevel` array for priority determination
- Uses `experienceState?.levelToHide` to determine target object

### Implementation Benefits
- ✅ Prevents invalid visibility operations
- ✅ Maintains logical level hierarchy
- ✅ Comprehensive error handling and logging
- ✅ Clear console feedback for debugging

## Part 2: ExperienceControls - First-Person Camera Snap System

### Enhancement Details
Created a new first-person camera snap system with precise positioning and rotation matching.

### Key Features

#### 1. First-Person Snap Function
```javascript
const snapCameraToFirstPerson = useCallback((targetObject) => {
  // Move camera to snapObject's exact position
  camera.position.copy(targetPosition);
  
  // Rotate camera to face same direction as snapObject
  camera.quaternion.copy(targetQuaternion);
  
  // Calculate forward-looking target point
  const forwardDirection = new THREE.Vector3(0, 0, -1);
  forwardDirection.applyQuaternion(targetQuaternion);
  const lookAtTarget = targetPosition.clone().add(forwardDirection.multiplyScalar(5));
}, [camera]);
```

#### 2. Control Management
- **Immediate disable**: OrbitControls disabled when snap is triggered
- **Timed re-enable**: Controls re-enabled after exactly 3 seconds
- **First-person behavior**: Controls behave as first-person controller at new location

#### 3. Camera Reset Function (Future Use)
```javascript
const resetCameraToLocation = useCallback((targetPosition, targetLookAt) => {
  // Smooth Math.lerp animation transitions
  // Re-enable experienceState.firstPersonView mode
  // Use new min/max distances from data.minDistance/maxDistance
}, [camera]);
```

### Animation Features
- **Smooth transitions**: Uses easing functions for natural movement
- **Error recovery**: Comprehensive error handling with fallbacks
- **State management**: Proper control enable/disable timing
- **Debug logging**: Detailed console output for troubleshooting

## Technical Implementation

### Error Handling Patterns
Both enhancements follow established error handling patterns:
```javascript
try {
  // Main logic with parameter validation
  if (!requiredParam) {
    console.warn("Function: Missing required parameters");
    return;
  }
  // Implementation...
} catch (error) {
  console.error('Error in function:', error);
  // Cleanup and fallback
}
```

### React 19 Compatibility
- ✅ Compatible with React 19 and current Three.js setup
- ✅ Uses React 19 compatible @react-three/fiber@rc
- ✅ Proper useCallback dependencies
- ✅ Safe destructuring with fallbacks

### Modular Architecture
- ✅ Functions kept under 500 lines each
- ✅ Clear separation of concerns
- ✅ Reusable utility functions
- ✅ Comprehensive documentation

## Files Modified

### Enhanced Files
- `src/components/experience/ExperienceModel.jsx`
  - Enhanced `handleLevelToHde` with priority-based logic
  - Added comprehensive error handling and logging
  - Improved useCallback dependencies

- `src/components/experience/ExperienceControls.jsx`
  - Added `snapCameraToFirstPerson` function
  - Added `resetCameraToLocation` function (for future use)
  - Enhanced activeRoomSnap handling
  - Improved control state management

### New Documentation
- `docs/camera-controls-enhancements.md` - This documentation

## Testing Recommendations

### Level Visibility Testing
1. Test with multiple hideLevel objects
2. Verify priority-based hiding/showing logic
3. Test with invalid object names
4. Check console logging for debugging

### Camera Snap Testing
1. Test first-person snap to various objects
2. Verify 3-second control disable/enable timing
3. Test camera rotation matching
4. Verify smooth transitions and error recovery

### Integration Testing
1. Test both enhancements together
2. Verify no conflicts with existing functionality
3. Test with various data configurations
4. Performance testing with complex scenes

## Usage Examples

### Level Visibility Control
```javascript
// In UI component, trigger level hide/show
experienceDispatch({
  type: ACTIONS_EXPERIENCE.LEVEL_TO_HIDE, 
  payload: { name: 'level1', /* other properties */ }
});
```

### Camera Snap Control
```javascript
// In UI component, trigger camera snap
experienceDispatch({
  type: ACTIONS_EXPERIENCE.SET_ACTIVE_ROOM_SNAP, 
  payload: 'snapPoint1.gltf'
});
```

## Future Enhancements
- Integration of resetCameraToLocation with UI controls
- Additional easing functions for different animation styles
- Configurable snap timing and animation parameters
- Advanced collision detection for camera positioning
