// scripts/test-user-management.js
// Automated testing script for user management system

import { MongoClient } from 'mongodb';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const uri = process.env.MONGODB_URI;
const baseUrl = 'https://localhost:3002';

// Test data
const testUsers = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'user'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    role: 'user'
  },
  {
    name: 'Admin Test',
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    role: 'admin'
  }
];

async function testUserManagementSystem() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    console.log('\n🧪 STARTING USER MANAGEMENT SYSTEM TESTS\n');
    
    // Test 1: Verify admin user exists and has proper credentials
    console.log('📋 Test 1: Verify Admin User');
    const adminUser = await usersCollection.findOne({ email: '<EMAIL>' });
    
    if (adminUser) {
      console.log('✅ Admin user found');
      console.log(`   Email: ${adminUser.email}`);
      console.log(`   Role: ${adminUser.role}`);
      console.log(`   Has Password: ${adminUser.password ? 'Yes' : 'No'}`);
      
      if (adminUser.password) {
        const passwordValid = await bcrypt.compare('AdminPassword123!', adminUser.password);
        console.log(`   Password Valid: ${passwordValid ? 'Yes' : 'No'}`);
      }
    } else {
      console.log('❌ Admin user not found');
      return;
    }
    
    // Test 2: Create test users
    console.log('\n📋 Test 2: Create Test Users');
    for (const testUser of testUsers) {
      try {
        // Check if user already exists
        const existingUser = await usersCollection.findOne({ email: testUser.email });
        
        if (existingUser) {
          console.log(`⚠️  User ${testUser.email} already exists, skipping creation`);
          continue;
        }
        
        // Hash password
        const hashedPassword = await bcrypt.hash(testUser.password, 10);
        
        // Create user
        const userData = {
          ...testUser,
          password: hashedPassword,
          invites: [],
          invoices: [],
          recipets: [],
          messgages: [],
          emailVerified: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        const result = await usersCollection.insertOne(userData);
        
        if (result.acknowledged) {
          console.log(`✅ Created user: ${testUser.email} (${testUser.role})`);
        } else {
          console.log(`❌ Failed to create user: ${testUser.email}`);
        }
      } catch (error) {
        console.log(`❌ Error creating user ${testUser.email}:`, error.message);
      }
    }
    
    // Test 3: Verify user count and roles
    console.log('\n📋 Test 3: Verify User Database');
    const totalUsers = await usersCollection.countDocuments();
    const adminUsers = await usersCollection.countDocuments({ role: 'admin' });
    const regularUsers = await usersCollection.countDocuments({ role: 'user' });
    
    console.log(`✅ Total Users: ${totalUsers}`);
    console.log(`✅ Admin Users: ${adminUsers}`);
    console.log(`✅ Regular Users: ${regularUsers}`);
    
    // Test 4: Test user queries (simulating API calls)
    console.log('\n📋 Test 4: Test User Queries');
    
    // Test pagination
    const page1Users = await usersCollection.find({})
      .sort({ createdAt: -1 })
      .limit(2)
      .toArray();
    console.log(`✅ Pagination test: Retrieved ${page1Users.length} users`);
    
    // Test search
    const searchResults = await usersCollection.find({
      $or: [
        { name: { $regex: 'john', $options: 'i' } },
        { email: { $regex: 'john', $options: 'i' } }
      ]
    }).toArray();
    console.log(`✅ Search test: Found ${searchResults.length} users matching 'john'`);
    
    // Test role filter
    const adminResults = await usersCollection.find({ role: 'admin' }).toArray();
    console.log(`✅ Role filter test: Found ${adminResults.length} admin users`);
    
    // Test 5: Test user operations
    console.log('\n📋 Test 5: Test User Operations');
    
    // Find a test user to update
    const testUser = await usersCollection.findOne({ email: '<EMAIL>' });
    
    if (testUser) {
      // Test update
      const updateResult = await usersCollection.updateOne(
        { _id: testUser._id },
        { 
          $set: { 
            name: 'John Doe Updated',
            updatedAt: new Date()
          }
        }
      );
      
      if (updateResult.modifiedCount > 0) {
        console.log('✅ User update test: Success');
      } else {
        console.log('❌ User update test: Failed');
      }
      
      // Verify update
      const updatedUser = await usersCollection.findOne({ _id: testUser._id });
      if (updatedUser.name === 'John Doe Updated') {
        console.log('✅ Update verification: Success');
      } else {
        console.log('❌ Update verification: Failed');
      }
    }
    
    // Test 6: Test security constraints
    console.log('\n📋 Test 6: Test Security Constraints');
    
    // Test unique email constraint
    try {
      const duplicateUser = {
        name: 'Duplicate User',
        email: '<EMAIL>', // Duplicate admin email
        password: await bcrypt.hash('TestPassword123!', 10),
        role: 'user',
        invites: [],
        invoices: [],
        recipets: [],
        messgages: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await usersCollection.insertOne(duplicateUser);
      console.log('❌ Email uniqueness test: Failed (duplicate allowed)');
    } catch (error) {
      if (error.code === 11000) {
        console.log('✅ Email uniqueness test: Success (duplicate rejected)');
      } else {
        console.log('⚠️  Email uniqueness test: Unexpected error:', error.message);
      }
    }
    
    // Test 7: Display final user list
    console.log('\n📋 Test 7: Final User List');
    const allUsers = await usersCollection.find({})
      .sort({ createdAt: -1 })
      .toArray();
    
    console.log('👥 All Users:');
    allUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} (${user.email}) - ${user.role}`);
    });
    
    console.log('\n🎉 USER MANAGEMENT SYSTEM TESTS COMPLETED!');
    console.log('\n📊 Test Summary:');
    console.log(`   ✅ Admin user verified`);
    console.log(`   ✅ Test users created`);
    console.log(`   ✅ Database queries working`);
    console.log(`   ✅ User operations functional`);
    console.log(`   ✅ Security constraints enforced`);
    console.log(`   📈 Total users in system: ${allUsers.length}`);
    
    console.log('\n🚀 Ready for UI testing!');
    console.log('   1. Login with: <EMAIL> / AdminPassword123!');
    console.log('   2. Access admin dashboard: https://localhost:3002/admin/users');
    console.log('   3. Test user management features');
    
  } catch (error) {
    console.error('❌ Error running tests:', error);
  } finally {
    await client.close();
  }
}

// Cleanup function to remove test users
async function cleanupTestUsers() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db();
    const usersCollection = db.collection('users');
    
    console.log('🧹 Cleaning up test users...');
    
    const testEmails = testUsers.map(user => user.email);
    const result = await usersCollection.deleteMany({
      email: { $in: testEmails }
    });
    
    console.log(`✅ Removed ${result.deletedCount} test users`);
    
  } catch (error) {
    console.error('❌ Error cleaning up:', error);
  } finally {
    await client.close();
  }
}

// Run tests or cleanup based on command line argument
const command = process.argv[2];

if (command === 'cleanup') {
  cleanupTestUsers();
} else {
  testUserManagementSystem();
}
