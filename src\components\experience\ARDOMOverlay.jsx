'use client'
import { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'
import { TbAugmentedReality2, TbHexagon3D, TbView360 } from 'react-icons/tb'

export default function ARDOMOverlay({ 
  placedModels, 
  reticleType, 
  currentHitPose, 
  hitTestReady, 
  sessionActive,
  onCycleReticle,
  onClearModels,
  onTestPlace,
  onToggleARSystem
}) {
  const { experienceState, experienceDispatch } = useExperienceContext()
  const [overlayContainer, setOverlayContainer] = useState(null)

  useEffect(() => {
    // Find or create the DOM overlay container
    let container = document.getElementById('ar-overlay-container')
    if (!container) {
      container = document.createElement('div')
      container.id = 'ar-overlay-container'
      container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
      `
      document.body.appendChild(container)
    }
    setOverlayContainer(container)

    return () => {
      // Cleanup on unmount
      if (container && container.parentNode) {
        container.parentNode.removeChild(container)
      }
    }
  }, [])

  const modes = [
    { icon: <TbView360 className="text-2xl" />, name: '360' },
    { icon: <TbHexagon3D className="text-2xl" />, name: 'Model' },
    { icon: <TbAugmentedReality2 className="text-2xl" />, name: 'AR' },
  ]

  const handleModeSwitch = (index) => {
    console.log('🔄 Switching mode:', index)
    if (index === 0) {
      experienceDispatch({ type: ACTIONS_EXPERIENCE.MODE_360 })
    } else if (index === 1) {
      experienceDispatch({ type: ACTIONS_EXPERIENCE.MODE_MODEL })
    } else if (index === 2) {
      experienceDispatch({ type: ACTIONS_EXPERIENCE.MODE_AR })
    }
  }

  if (!overlayContainer) {
    return null
  }

  return createPortal(
    <div className="ar-overlay-ui" style={{ pointerEvents: 'none', width: '100%', height: '100%' }}>
      {/* Mode Switcher - Top Center */}
      <div 
        className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50"
        style={{ pointerEvents: 'auto' }}
      >
        <div className="flex gap-2 bg-black/70 text-white p-2 rounded-lg shadow-lg">
          {modes.map((mode, index) => (
            <button
              key={index}
              onClick={() => handleModeSwitch(index)}
              className={`p-3 rounded-lg transition-colors ${
                (index === 0 && experienceState?.mode360) ||
                (index === 1 && experienceState?.modeModel) ||
                (index === 2 && experienceState?.modeAR)
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-600 hover:bg-gray-500 text-gray-200'
              }`}
              title={mode.name}
            >
              {mode.icon}
            </button>
          ))}
        </div>
      </div>

      {/* AR Controls - Top Right */}
      <div 
        className="absolute top-4 right-4 z-50"
        style={{ pointerEvents: 'auto' }}
      >
        <div className="bg-black/70 text-white p-3 rounded-lg shadow-lg">
          <h3 className="font-bold text-sm mb-2">AR Controls</h3>
          <div className="space-y-2 text-xs">
            <div>Models: {placedModels}</div>
            <div>Reticle: {reticleType}</div>
            <div>Hit Pose: {currentHitPose ? '✅' : '❌'}</div>
            <div>Hit Test Ready: {hitTestReady ? '✅' : '❌'}</div>
            <div>Session Active: {sessionActive ? '✅' : '❌'}</div>
          </div>
          <div className="flex gap-2 mt-3">
            <button
              onClick={onCycleReticle}
              className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
            >
              Cycle Reticle
            </button>
            <button
              onClick={onClearModels}
              className="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs transition-colors"
            >
              Clear All
            </button>
          </div>
          <div className="flex gap-2 mt-2">
            <button
              onClick={onTestPlace}
              className="bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs transition-colors"
            >
              Test Place
            </button>
            <button
              onClick={onToggleARSystem}
              className="bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded text-xs transition-colors"
            >
              Legacy AR
            </button>
          </div>
        </div>
      </div>

      {/* Instructions - Bottom Center */}
      <div 
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50"
        style={{ pointerEvents: 'auto' }}
      >
        <div className="bg-black/70 text-white p-3 rounded-lg shadow-lg text-center">
          <p className="text-sm">
            Point your device at a flat surface and tap to place a building
          </p>
        </div>
      </div>

      {/* Debug Info - Bottom Left */}
      <div 
        className="absolute bottom-4 left-4 z-50"
        style={{ pointerEvents: 'none' }}
      >
        <div className="bg-black/50 text-white p-2 rounded text-xs">
          <div>DOM Overlay Active</div>
          <div>UI Clickable: ✅</div>
        </div>
      </div>
    </div>,
    overlayContainer
  )
}
