// lib/models.js
// This file defines the Mongoose schemas and models required by NextAuth.js,
// as well as a custom User model for credentials.
import mongoose, { Schema } from 'mongoose';

// Connect to DB (ensure connection before defining models)
// This is not strictly necessary here, as NextAuth.js adapter will call connect,
// but useful if you need to access models directly in other parts of your app.
// import dbConnect from './mongodb';
// dbConnect();

// Define a schema for the User model, including fields for credentials.
// NextAuth.js Mongoose adapter will automatically add/manage fields
// like 'emailVerified', 'image', 'accounts', 'sessions'.
// We explicitly add 'password' for the credentials provider.
const userSchema = new Schema({
  name: {
    type: String,
    required: false, // Name is optional for OAuth users
  },
  email: {
    type: String,
    unique: true,
    required: true,
  },
  password: {
    type: String,
    required: false, // Password is not required for OAuth users
  },
  // NextAuth.js Mongoose adapter will add:
  // emailVerified: Date,
  // image: String,
  // accounts: [Schema.Types.ObjectId], // References to Account model
  // sessions: [Schema.Types.ObjectId], // References to Session model
}, { timestamps: true }); // Timestamps are useful for created/updated dates

// Define schema for Accounts (OAuth connections)
const accountSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  type: { type: String, required: true },
  provider: { type: String, required: true },
  providerAccountId: { type: String, required: true },
  refresh_token: String,
  access_token: String,
  expires_at: Number,
  token_type: String,
  scope: String,
  id_token: String,
  session_state: String,
}, { timestamps: true });
accountSchema.index({ provider: 1, providerAccountId: 1 }, { unique: true });

// Define schema for Sessions (used if session strategy is 'database')
// Not strictly needed if session strategy is 'jwt', but good practice to define.
const sessionSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  expires: { type: Date, required: true },
  sessionToken: { type: String, unique: true, required: true },
}, { timestamps: true });

// Define schema for VerificationTokens (for email/magic links)
const verificationTokenSchema = new Schema({
  identifier: { type: String, required: true }, // Typically email
  token: { type: String, unique: true, required: true },
  expires: { type: Date, required: true },
});
verificationTokenSchema.index({ identifier: 1, token: 1 }, { unique: true });

// Check if models are already defined to prevent Mongoose error on hot reload
const User = mongoose.models.User || mongoose.model('User', userSchema);
const Account = mongoose.models.Account || mongoose.model('Account', accountSchema);
const Session = mongoose.models.Session || mongoose.model('Session', sessionSchema);
const VerificationToken = mongoose.models.VerificationToken || mongoose.model('VerificationToken', verificationTokenSchema);

export { User, Account, Session, VerificationToken };
