/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        domains: ['firebasestorage.googleapis.com'],
        unoptimized: true,
    },
    turbopack: {
        rules: {
            '*.svg': {
                loaders: ['@svgr/webpack'],
                as: '*.js',
            },
        },
    },
    webpack: (config, { isServer }) => {
        // Handle font loading issues
        config.resolve.fallback = {
            ...config.resolve.fallback,
            fs: false,
            net: false,
            tls: false,
        };

        return config;
    },
    // Suppress specific warnings
    onDemandEntries: {
        maxInactiveAge: 25 * 1000,
        pagesBufferLength: 2,
    },
    // Improve hydration handling
    reactStrictMode: true,
    // Suppress hydration warnings in development
    ...(process.env.NODE_ENV === 'development' && {
        logging: {
            fetches: {
                fullUrl: true,
            },
        },
    }),
};

export default nextConfig;
