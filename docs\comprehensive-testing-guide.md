# Comprehensive Testing Guide - User Management System

## 🎯 **TESTING OVERVIEW**

### **Current Status**
- ✅ **Authentication**: Working with admin credentials
- ✅ **Session Management**: JWT tokens being created successfully
- ✅ **Server**: Running on `https://localhost:3002`
- ✅ **Admin User**: `<EMAIL>` / `AdminPassword123!`

## 📋 **STEP-BY-STEP TESTING CHECKLIST**

### **PHASE 1: AUTHENTICATION TESTING**

#### **✅ Test 1.1: Admin Credentials Login**
1. **Navigate to**: `https://localhost:3002/auth/signin`
2. **Enter credentials**:
   - Email: `<EMAIL>`
   - Password: `AdminPassword123!`
3. **Expected Result**: Successful login and redirect to dashboard
4. **Verify**: Check server logs for "Successful credentials authentication"

#### **✅ Test 1.2: Session Persistence**
1. **After login**: Refresh the page
2. **Expected Result**: User remains logged in
3. **Verify**: Session token in browser storage

#### **✅ Test 1.3: AuthPopup Integration**
1. **Navigate to**: `https://localhost:3002/` (home page)
2. **Click**: Authentication button/icon to open AuthPopup
3. **Expected Result**: 
   - Shows user profile with name and email
   - Shows "Dashboard" link (admin user)
   - Shows "Profile Settings" link
   - Shows "Sign Out" button

### **PHASE 2: USER MANAGEMENT SYSTEM TESTING**

#### **✅ Test 2.1: Admin Dashboard Access**
1. **Navigate to**: `https://localhost:3002/admin/users`
2. **Expected Result**: 
   - User management dashboard loads
   - Shows list of users (at least admin user)
   - Shows search and filter options
   - Shows "Create New User" button

#### **✅ Test 2.2: User List Functionality**
1. **Search Test**: Enter "victor" in search box
2. **Filter Test**: Select "admin" from role filter
3. **Expected Result**: 
   - Search filters results correctly
   - Role filter shows only admin users
   - Pagination works if multiple users

#### **✅ Test 2.3: Create New User**
1. **Click**: "Create New User" button
2. **Navigate to**: `/admin/users/create`
3. **Fill form**:
   - Name: "Test User"
   - Email: "<EMAIL>"
   - Password: "TestPassword123!"
   - Confirm Password: "TestPassword123!"
   - Role: "user"
4. **Submit**: Click "Create User"
5. **Expected Result**: 
   - User created successfully
   - Redirected to user list
   - New user appears in list

#### **✅ Test 2.4: View User Details**
1. **Click**: "View" button next to any user
2. **Expected Result**: 
   - User details page loads
   - Shows complete user information
   - Shows account statistics
   - Shows edit and delete buttons

#### **✅ Test 2.5: Edit User**
1. **Click**: "Edit" button on user details page
2. **Modify**: User name or other fields
3. **Submit**: Click "Update User"
4. **Expected Result**: 
   - User updated successfully
   - Changes reflected in user list

#### **✅ Test 2.6: Delete User (Non-Admin)**
1. **Navigate**: Back to user list
2. **Click**: "Delete" button next to test user (not admin)
3. **Confirm**: Deletion in popup
4. **Expected Result**: 
   - User deleted successfully
   - Removed from user list

### **PHASE 3: OAUTH ACCOUNT LINKING TESTING**

#### **✅ Test 3.1: OAuth Conflict Simulation**
1. **Sign out**: From current session
2. **Try Google Sign-in**: Using `<EMAIL>`
3. **Expected Result**: 
   - Should show "OAuthAccountNotLinked" error
   - Redirected to sign-in page with error message
   - Clear instructions for account linking

#### **✅ Test 3.2: Account Linking Flow**
1. **After OAuth error**: Follow instructions on sign-in page
2. **Sign in**: With credentials (`<EMAIL>` / `AdminPassword123!`)
3. **Expected Result**: 
   - Successful credentials login
   - Prompt to link Google account
   - Option to complete linking

### **PHASE 4: ROLE-BASED ACCESS CONTROL TESTING**

#### **✅ Test 4.1: Admin Access Verification**
1. **As admin user**: Access all admin pages
   - `/admin/users` - Should work
   - `/admin/users/create` - Should work
   - `/admin/users/[id]` - Should work
   - `/admin/users/[id]/edit` - Should work

#### **✅ Test 4.2: User Access Restrictions**
1. **Create regular user**: With role "user"
2. **Login as regular user**: Test access
3. **Try accessing**: `/admin/users`
4. **Expected Result**: 
   - Redirected to "not-authorized" page
   - Cannot access admin-only features

#### **✅ Test 4.3: Self-Edit Permissions**
1. **As regular user**: Try to edit own profile
2. **Navigate to**: `/admin/users/[own-id]/edit`
3. **Expected Result**: 
   - Can edit own profile
   - Cannot change own role
   - Cannot access other users

### **PHASE 5: AUTHPOPUP COMPONENT TESTING**

#### **✅ Test 5.1: Authenticated State**
1. **When logged in**: Open AuthPopup
2. **Verify displays**:
   - User avatar (or initials)
   - User name and email
   - Dashboard link (for admin)
   - Profile Settings link
   - Sign Out button

#### **✅ Test 5.2: Unauthenticated State**
1. **When logged out**: Open AuthPopup
2. **Verify displays**:
   - "Continue with Google" button
   - "Continue with Facebook" button
   - "Sign in with Email" link
   - "Register" link

#### **✅ Test 5.3: OAuth Integration**
1. **Click**: "Continue with Google" in AuthPopup
2. **Expected Result**: 
   - Initiates Google OAuth flow
   - Handles account linking if needed
   - Shows appropriate error messages

## 🔧 **IDENTIFIED IMPROVEMENTS FOR AUTHPOPUP**

### **Issue 1: Admin Role Detection**
**Current Code** (line 130):
```javascript
{session.user.isAdmin && (
```

**Problem**: Should check `session.user.role === 'admin'` instead of `isAdmin`

**Fix Needed**: Update role checking logic

### **Issue 2: Admin Dashboard Link**
**Current**: Links to `/dashboard`
**Should**: Link to `/admin/users` for admin users

### **Issue 3: OAuth Error Handling**
**Current**: Basic error alerts
**Should**: Integrate with account linking flow

## 🛠 **REQUIRED AUTHPOPUP FIXES**

Let me implement these fixes now:

### **Fix 1: Correct Admin Role Detection**
```javascript
// Change line 130 from:
{session.user.isAdmin && (
// To:
{session.user.role === 'admin' && (
```

### **Fix 2: Proper Admin Dashboard Link**
```javascript
// Change Dashboard link to point to admin panel
href="/admin/users"
```

### **Fix 3: Enhanced OAuth Error Handling**
```javascript
// Add account linking detection in handleSocialSignIn
if (result?.error === 'OAuthAccountNotLinked') {
  window.location.href = `/auth/signin?error=OAuthAccountNotLinked&email=${session.user.email}`;
}
```

## 📊 **TESTING RESULTS TRACKING**

### **Authentication Tests**
- [ ] Admin login works
- [ ] Session persistence works
- [ ] AuthPopup shows correct authenticated state

### **User Management Tests**
- [ ] Admin dashboard accessible
- [ ] User CRUD operations work
- [ ] Search and filter functional
- [ ] Role-based access enforced

### **OAuth Tests**
- [ ] Account linking error detected
- [ ] Linking flow works correctly
- [ ] Error messages clear and helpful

### **Security Tests**
- [ ] Non-admin users blocked from admin pages
- [ ] Users can only edit own profiles
- [ ] Cannot delete own account
- [ ] Cannot remove own admin status

## 🎯 **NEXT ACTIONS**

1. **Fix AuthPopup Component**: Implement the identified improvements
2. **Run Complete Test Suite**: Follow the testing checklist
3. **Document Results**: Record any issues found
4. **Performance Testing**: Test with multiple users
5. **Security Audit**: Verify all access controls

## 📝 **TESTING NOTES**

- **Server Logs**: Monitor for authentication success/failure
- **Browser Console**: Check for JavaScript errors
- **Network Tab**: Verify API calls are successful
- **Database**: Confirm data persistence

**Ready to begin comprehensive testing!** 🚀
